- the next draw date should be the relative date without "NEXT DRAW" text such as
    - if < 1 hour, show "In xx minutes"
    - if < 1 day, show "In xx hours"
    - if > 1 day and < 2 days, show "Tomorrow, x PM"
    - if >= 2 days, show "In xx days"
    - drawed and pending results -> show "Pending Results" on cash view and "Drawed xx hours ago" in date
        -> how do we know the status of "Pending Results"? no next draw date or something?


- Show what type of winner such as "5 MATCH"
- Check when the app or widget call the API
- check widget in visionOS
- add a video link of drawing?

===

- how could you design the ios widget if you are apple engineer and it's shipped in OS default? which doesn't hurt the look and feel with other apps.
- design widget for visionOS
- when the jackpot is increased after draw, use the animation for increasing or decreasing number
- Connect to the backup server when failed to parsed the data
- send money to font developer

===

- Scan the barcode of the ticket and verify the ticket when the time is up with push notification