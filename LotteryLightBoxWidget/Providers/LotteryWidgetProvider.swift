//
//  LotteryWidgetProvider.swift
//  LotteryLightBoxWidget
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/11/25.
//

import Foundation
import WidgetKit

struct LotteryWidgetProvider: TimelineProvider {
    func placeholder(in context: Context) -> LotteryEntry {
        LotteryEntry(date: Date(), lotteryData: [:])
    }

    func getSnapshot(in context: Context, completion: @escaping (LotteryEntry) -> ()) {
        let lotteryData = WidgetDataManager.loadAllData()
        let entry = LotteryEntry(date: Date(), lotteryData: lotteryData)
        completion(entry)
    }
    
    func getTimeline(in context: Context, completion: @escaping (Timeline<LotteryEntry>) -> ()) {
        let lotteryData = WidgetDataManager.loadAllData()
        
        // Generate multiple timeline entries for smoother updates
        let entries = generateTimelineEntries(from: lotteryData)
        
        // Calculate when we should refresh the entire timeline
        let nextTimelineRefresh = calculateNextRefreshDate(from: lotteryData)
        
        // Use .atEnd policy to reload timeline immediately after the last entry
        // This ensures more frequent updates and better responsiveness
        let timeline = Timeline(entries: entries, policy: .atEnd)
        completion(timeline)
    }
    
    private func generateTimelineEntries(from lotteryData: [LotteryType: DrawingInfo]) -> [LotteryEntry] {
        var entries: [LotteryEntry] = []
        let now = Date()
        
        // Always include current entry
        entries.append(LotteryEntry(date: now, lotteryData: lotteryData))
        
        // Check if we have any upcoming drawings
        let hasUpcomingDrawing = lotteryData.values.contains { drawingInfo in
            let timeUntilDraw = drawingInfo.nextDrawingDate.timeIntervalSince(now)
            return timeUntilDraw > 0 && timeUntilDraw <= 24 * 60 * 60 // Within 24 hours
        }
        
        // Check if any data is stale (more than 30 minutes old)
        let hasStaleData = lotteryData.isEmpty || lotteryData.values.contains { drawingInfo in
            let timeSinceLastUpdate = now.timeIntervalSince(drawingInfo.drawingDate)
            return timeSinceLastUpdate > 30 * 60 // 30 minutes
        }
        
        if hasUpcomingDrawing {
            // More frequent updates when drawing is approaching
            let intervals = [5, 15, 30, 60] // 5, 15, 30, 60 minutes from now
            for minutes in intervals {
                let entryDate = Calendar.current.date(byAdding: .minute, value: minutes, to: now) ?? now
                entries.append(LotteryEntry(date: entryDate, lotteryData: lotteryData))
            }
        } else if hasStaleData {
            // Frequent updates when data is stale
            let intervals = [2, 5, 15, 30] // 2, 5, 15, 30 minutes from now
            for minutes in intervals {
                let entryDate = Calendar.current.date(byAdding: .minute, value: minutes, to: now) ?? now
                entries.append(LotteryEntry(date: entryDate, lotteryData: lotteryData))
            }
        } else {
            // Standard updates for normal times
            let intervals = [15, 30, 60] // 15, 30, 60 minutes from now  
            for minutes in intervals {
                let entryDate = Calendar.current.date(byAdding: .minute, value: minutes, to: now) ?? now
                entries.append(LotteryEntry(date: entryDate, lotteryData: lotteryData))
            }
        }
        
        return entries
    }
    
    private func calculateNextRefreshDate(from lotteryData: [LotteryType: DrawingInfo]) -> Date {
        let currentDate = Date()
        var earliestNextDraw = Date.distantFuture
        
        // Find the earliest upcoming drawing date
        for (_, drawingInfo) in lotteryData {
            if drawingInfo.nextDrawingDate < earliestNextDraw {
                earliestNextDraw = drawingInfo.nextDrawingDate
            }
        }
        
        // If we have a valid next drawing date and it's in the future
        if earliestNextDraw != Date.distantFuture && earliestNextDraw > currentDate {
            // Schedule refresh 15 minutes after the next drawing (reduced from 30)
            return Calendar.current.date(byAdding: .minute, value: 15, to: earliestNextDraw) ?? currentDate.addingTimeInterval(15 * 60)
        }
        
        // Check if any current data is expired or stale
        let hasExpiredData = lotteryData.values.contains { drawingInfo in
            currentDate > drawingInfo.nextDrawingDate
        }
        
        let hasStaleData = lotteryData.isEmpty || lotteryData.values.contains { drawingInfo in
            let timeSinceLastUpdate = currentDate.timeIntervalSince(drawingInfo.drawingDate)
            return timeSinceLastUpdate > 30 * 60 // 30 minutes
        }
        
        if hasExpiredData || hasStaleData {
            // If data is expired or stale, refresh in 5 minutes (reduced from 15)
            return Calendar.current.date(byAdding: .minute, value: 5, to: currentDate) ?? currentDate.addingTimeInterval(5 * 60)
        }
        
        // Default: refresh in 15 minutes (reduced from 30)
        return Calendar.current.date(byAdding: .minute, value: 15, to: currentDate) ?? currentDate.addingTimeInterval(15 * 60)
    }
}
