//
//  LotteryWidgetCard.swift
//  LotteryLightBoxWidget
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/11/25.
//

import SwiftUI
import WidgetKit

struct LotteryWidgetCard: View {
    let info: DrawingInfo
    
    // MARK: - Constants
    private let headerTextSize: CGFloat = 12
    private let jackpotTextSize: CGFloat = 70
    private let cashTextSize: CGFloat = 12
    private let nextDrawTextSize: CGFloat = 10
    
    private let cardSpacing: CGFloat = 6
    private let cardPadding: CGFloat = 8

    var body: some View {
        VStack(spacing: cardSpacing) {
            // Lottery header
            HStack(alignment: .center) {
                WidgetLabelView(
                    info.lotteryType.rawValue.uppercased(),
                    color: .white,
                    size: headerTextSize
                ).font(.system(size: headerTextSize, weight: .black, design: .rounded))
            }
            
            // Jackpot amount
            WidgetJackpotView(DataFormatter.formatWidgetJackpot(info.jackpot),
                              color: .red,
                              size: jackpotTextSize)
            
            // Cash value
            WidgetLabelView(DataFormatter.formatWidgetCash(info.cash),
                            color: .green,
                            size: cashTextSize)
            
            // Next drawing - using current date from widget timeline
            WidgetLabelView(
                DataFormatter.formatWidgetNextDrawingDate(info.nextDrawingDate),
                color: .gray,
                size: nextDrawTextSize
            )
        }
        .padding(cardPadding)
    }
} 

