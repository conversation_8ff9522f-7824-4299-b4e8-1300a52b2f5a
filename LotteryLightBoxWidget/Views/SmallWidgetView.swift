//
//  SmallWidgetView.swift
//  LotteryLightBoxWidget
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/11/25.
//

import SwiftUI
import WidgetKit

struct SmallWidgetView: View {
    let info: DrawingInfo?
    let type: LotteryType
    
    var body: some View {
        if let info = info {
            LotteryWidgetCard(info: info)
        } else {
            ErrorWidgetCard(lotteryType: type)
        }
    }
}
