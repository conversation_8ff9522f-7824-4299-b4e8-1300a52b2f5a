//
//  ErrorWidgetCard.swift
//  LotteryLightBoxWidget
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/11/25.
//

import SwiftUI
import WidgetKit

struct ErrorWidgetCard: View {
    let lotteryType: LotteryType
    
    var body: some View {
        VStack(spacing: 6) {
            HStack {
                Circle()
                    .fill(lotteryType == .powerball ? .red : .yellow)
                    .frame(width: 8, height: 8)
                
                WidgetLabelView(lotteryType.rawValue,
                                color: lotteryType == .powerball ? .red : .yellow,
                                size: 12)
                
                Spacer()
            }
            
            WidgetTextView("ERROR", color: .red, size: 16, glowIntensity: 1.0)
            WidgetLabelView("NO DATA", color: .red, size: 12)
        }
        .padding(8)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(Color.black)
                .overlay(
                    RoundedRectangle(cornerRadius: 6)
                        .stroke(Color.red, lineWidth: 1)
                )
        )
    }
} 
