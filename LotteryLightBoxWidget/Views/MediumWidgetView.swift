//
//  MediumWidgetView.swift
//  LotteryLightBoxWidget
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/11/25.
//

import SwiftUI
import WidgetKit

struct MediumWidgetView: View {
    let entry: LotteryEntry
    
    var body: some View {
        HStack(spacing: 40) {
            ForEach([LotteryType.powerball, LotteryType.megaMillions], id: \.self) { lotteryType in
                if let info = entry.lotteryData[lotteryType] {
                    LotteryWidgetCard(info: info)
                } else {
                    ErrorWidgetCard(lotteryType: lotteryType)
                }
            }
        }
    }
}
