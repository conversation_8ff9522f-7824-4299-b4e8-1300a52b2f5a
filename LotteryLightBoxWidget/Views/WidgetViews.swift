//
//  WidgetViews.swift
//  LotteryLightBoxWidget
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/8/25.
//

import SwiftUI
import WidgetKit

struct WidgetTextView: View {
    let font: Font
    let text: String
    let color: Color
    let glowIntensity: CGFloat
    
    @Environment(\.font) var environmentFont
    
    init(_ text: String, color: Color = .red, size: CGFloat = 24, glowIntensity: CGFloat = 0.8) {
        self.text = text
        self.color = color
        self.glowIntensity = glowIntensity
        self.font = Font.system(size: size, weight: .bold, design: .monospaced)
    }
    
    var body: some View {
        Text(text)
            .font(environmentFont ?? font)
            .foregroundColor(color)
            .shadow(color: color.opacity(glowIntensity), radius: 4, x: 0, y: 0)
            .shadow(color: color.opacity(glowIntensity * 0.5), radius: 8, x: 0, y: 0)
    }
}

struct WidgetJackpotView: View {
    let number: String
    let color: Color
    let size: CGFloat
    
    init(_ number: String, color: Color = .red, size: CGFloat) {
        self.number = number
        self.color = color
        self.size = size
    }
    
    var body: some View {
        WidgetTextView(number, color: color, glowIntensity: 0.5)
            .font(.custom("DS-DIGITAL-Bold", size: size))
    }
}

struct WidgetLabelView: View {
    let text: String
    let color: Color
    let size: CGFloat
    
    init(_ text: String, color: Color = .blue, size: CGFloat = 16) {
        self.text = text
        self.color = color
        self.size = size
    }
    
    var body: some View {
        WidgetTextView(text, color: color, size: size, glowIntensity: 0.0)
    }
} 
