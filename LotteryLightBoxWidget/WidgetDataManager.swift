//
//  WidgetDataManager.swift
//  LotteryLightBoxWidget
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/8/25.
//

import Foundation

struct WidgetDataManager {
    private static let appGroupIdentifier = "group.koo.jaesung.LotteryLightbox"
    
    private static func storageKey(for lotteryType: LotteryType) -> String {
        switch lotteryType {
        case .powerball:
            return "powerball_data"
        case .megaMillions:
            return "megamillions_data"
        case .superLottoPlus:
            return "superlotto_data"
        }
    }
    
    static func loadData(for lotteryType: LotteryType) -> DrawingInfo? {
        let key = storageKey(for: lotteryType)
        guard let userDefaults = UserDefaults(suiteName: appGroupIdentifier) else { 
            print("Widget: Failed to create UserDefaults with app group identifier: \(appGroupIdentifier)")
            return nil 
        }
        
        guard let data = userDefaults.data(forKey: key) else { 
            print("Widget: No \(lotteryType) data found in App Group")
            return nil 
        }
        
        print("Widget: Found \(lotteryType) data: \(data.count) bytes")
        
        do {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            let result = try decoder.decode(DrawingInfo.self, from: data)
            print("Widget: Successfully loaded \(lotteryType) data from App Group")
            return result
        } catch {
            print("Widget: Failed to load \(lotteryType) data: \(error)")
            return nil
        }
    }
    
    static func loadAllData() -> [LotteryType: DrawingInfo] {
        var lotteryData: [LotteryType: DrawingInfo] = [:]
        
        for lotteryType in LotteryType.allCases {
            if let data = loadData(for: lotteryType) {
                lotteryData[lotteryType] = data
            }
        }
        
        return lotteryData
    }
} 
