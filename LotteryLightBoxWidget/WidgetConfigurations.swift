//
//  WidgetConfigurations.swift
//  LotteryLightBoxWidget
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/11/25.
//

import SwiftUI
import WidgetKit

// MARK: - Widget Configurations
struct LotteryLightBoxWidget: Widget {
    let kind: String = "LotteryLightBoxWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: LotteryWidgetProvider()) { entry in
            MediumWidgetView(entry: entry)
                .containerBackground(for: .widget) {
                    Color.black
                }
        }
        .configurationDisplayName("Lottery Lightbox")
        .description("Display lottery jackpots in authentic lightbox style")
        .supportedFamilies([.systemMedium])
        .contentMarginsDisabled()
    }
}

struct PowerballWidget: Widget {
    let kind: String = "PowerballWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: LotteryWidgetProvider()) { entry in
            SmallWidgetView(info: entry.lotteryData[.powerball], type: .powerball)
                .containerBackground(for: .widget) {
                    Color.black
                }
        }
        .configurationDisplayName("Powerball")
        .description("Powerball jackpot in authentic lightbox style")
        .supportedFamilies([.systemSmall])
        .contentMarginsDisabled()
    }
}

struct MegaMillionsWidget: Widget {
    let kind: String = "MegaMillionsWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: LotteryWidgetProvider()) { entry in
            SmallWidgetView(info: entry.lotteryData[.megaMillions], type: .megaMillions)
                .containerBackground(for: .widget) {
                    Color.black
                }
        }
        .configurationDisplayName("Mega Millions")
        .description("Mega Millions jackpot in authentic lightbox style")
        .supportedFamilies([.systemSmall])
        .contentMarginsDisabled()
    }
}

struct SuperLottoWidget: Widget {
    let kind: String = "SuperLottoWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: LotteryWidgetProvider()) { entry in
            SmallWidgetView(info: entry.lotteryData[.superLottoPlus], type: .superLottoPlus)
                .containerBackground(for: .widget) {
                    Color.black
                }
        }
        .configurationDisplayName("Super Lotto")
        .description("Super Lotto jackpot in authentic lightbox style")
        .supportedFamilies([.systemSmall])
        .contentMarginsDisabled()
    }
}

// MARK: - Previews
#Preview("Lottery Lightbox Widget - Medium", as: .systemMedium) {
    LotteryLightBoxWidget()
} timeline: {
    // Sample data for both lottery types
    let samplePowerballInfo = DrawingInfo(
        jackpot: "$1.2 Billion",
        cash: "$551.7 Million",
        drawingDate: Date(),
        nextDrawingDate: Calendar.current.date(byAdding: .day, value: 3, to: Date()) ?? Date(),
        winningNumbers: [7, 10, 24, 32, 57],
        theBall: 24,
        winners: [],
        lotteryType: .powerball
    )
    
    let sampleMegaMillionsInfo = DrawingInfo(
        jackpot: "$820 Million",
        cash: "$422.3 Million",
        drawingDate: Date(),
        nextDrawingDate: Calendar.current.date(byAdding: .day, value: 2, to: Date()) ?? Date(),
        winningNumbers: [13, 22, 39, 48, 62],
        theBall: 15,
        winners: [],
        lotteryType: .megaMillions
    )
    
    let lotteryData: [LotteryType: DrawingInfo] = [
        .powerball: samplePowerballInfo,
        .megaMillions: sampleMegaMillionsInfo
    ]
    
    LotteryEntry(date: Date(), lotteryData: lotteryData)
}

#Preview("Powerball Widget - Small", as: .systemSmall) {
    PowerballWidget()
} timeline: {
    let samplePowerballInfo = DrawingInfo(
        jackpot: "$1.2 Billion",
        cash: "$551.7 Million",
        drawingDate: Date(),
        nextDrawingDate: Calendar.current.date(byAdding: .day, value: 3, to: Date()) ?? Date(),
        winningNumbers: [7, 10, 24, 32, 57],
        theBall: 24,
        winners: [],
        lotteryType: .powerball
    )
    
    LotteryEntry(date: Date(), lotteryData: [.powerball: samplePowerballInfo])
}

#Preview("Mega Millions Widget - Small", as: .systemSmall) {
    MegaMillionsWidget()
} timeline: {
    let sampleMegaMillionsInfo = DrawingInfo(
        jackpot: "$820 Million",
        cash: "$422.3 Million",
        drawingDate: Date(),
        nextDrawingDate: Calendar.current.date(byAdding: .day, value: 2, to: Date()) ?? Date(),
        winningNumbers: [13, 22, 39, 48, 62],
        theBall: 15,
        winners: [],
        lotteryType: .megaMillions
    )
    
    LotteryEntry(date: Date(), lotteryData: [.megaMillions: sampleMegaMillionsInfo])
}

// MARK: - Preview with no data (loading state)
#Preview("Powerball Widget - Loading", as: .systemSmall) {
    PowerballWidget()
} timeline: {
    LotteryEntry(date: Date(), lotteryData: [:])
}

#Preview("Mega Millions Widget - Loading", as: .systemSmall) {
    MegaMillionsWidget()
} timeline: {
    LotteryEntry(date: Date(), lotteryData: [:])
}
