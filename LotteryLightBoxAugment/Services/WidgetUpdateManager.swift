//
//  WidgetUpdateManager.swift
//  LotteryLightBoxAugment
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/11/25.
//

import Foundation
import WidgetKit

class WidgetUpdateManager {
    static let shared = WidgetUpdateManager()
    
    private let widgetKinds = [
        "LotteryLightBoxWidget",
        "PowerballWidget", 
        "MegaMillionsWidget",
        "SuperLottoWidget"
    ]
    
    private init() {}
    
    // MARK: - Public Widget Update Methods
    
    /// Forces immediate update of all widgets
    func updateAllWidgets() {
        print("WidgetUpdateManager: Forcing update of all widgets...")
        WidgetCenter.shared.reloadAllTimelines()
        
        // Also reload specific widget kinds for redundancy
        for kind in widgetKinds {
            WidgetCenter.shared.reloadTimelines(ofKind: kind)
        }
        
        print("WidgetUpdateManager: All widget timelines reloaded")
    }
    
    /// Updates widgets only if data has been recently updated
    func updateWidgetsIfDataChanged() {
        let dataManager = LotteryDataManager.shared
        
        // Check if any data was updated in the last 5 minutes
        let hasRecentUpdates = LotteryType.allCases.contains { lotteryType in
            dataManager.hasRecentDataUpdate(for: lotteryType, within: 5 * 60)
        }
        
        if hasRecentUpdates {
            print("WidgetUpdateManager: Recent data updates detected, updating widgets...")
            updateAllWidgets()
        } else {
            print("WidgetUpdateManager: No recent data updates, skipping widget update")
        }
    }
    
    /// Updates widgets if the data is stale
    func updateWidgetsIfDataStale() {
        let dataManager = LotteryDataManager.shared
        
        if dataManager.hasAnyStaleData() {
            print("WidgetUpdateManager: Stale data detected, updating widgets...")
            updateAllWidgets()
        } else {
            print("WidgetUpdateManager: Data is fresh, no widget update needed")
        }
    }
    
    /// Updates a specific widget kind
    func updateWidget(kind: String) {
        guard widgetKinds.contains(kind) else {
            print("WidgetUpdateManager: Unknown widget kind: \(kind)")
            return
        }
        
        print("WidgetUpdateManager: Updating widget kind: \(kind)")
        WidgetCenter.shared.reloadTimelines(ofKind: kind)
    }
    
    /// Updates widgets based on lottery type
    func updateWidget(for lotteryType: LotteryType) {
        let kind: String
        switch lotteryType {
        case .powerball:
            kind = "PowerballWidget"
        case .megaMillions:
            kind = "MegaMillionsWidget"
        case .superLottoPlus:
            kind = "SuperLottoWidget"
        }
        
        updateWidget(kind: kind)
        
        // Also update the combined widget
        updateWidget(kind: "LotteryLightBoxWidget")
    }
    
    // MARK: - Convenience Methods
    
    /// Call this when the app becomes active
    func handleAppBecameActive() {
        print("WidgetUpdateManager: App became active")
        updateWidgetsIfDataStale()
    }
    
    /// Call this after successful data refresh
    func handleDataRefreshCompleted() {
        print("WidgetUpdateManager: Data refresh completed")
        updateAllWidgets()
    }
    
    /// Call this when the app will enter foreground
    func handleAppWillEnterForeground() {
        print("WidgetUpdateManager: App will enter foreground")
        // Give a small delay to let any background data fetching complete
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.updateWidgetsIfDataChanged()
        }
    }
} 