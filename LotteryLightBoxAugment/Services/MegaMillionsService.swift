//
//  MegaMillionsService.swift
//  LotteryLightBoxAugment
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/8/25.
//

import Foundation

class MegaMillionsService: LotteryServiceProtocol {
    var urlString = "https://www.megamillions.com/cmspages/utilservice.asmx/GetLatestDrawData"
    var response: String?
    
    init(response: String? = nil) {
        self.response = response
    }
    
    func fetchNextDrawingInfo() async throws -> DrawingInfo {
        if response == nil {
            guard let url = URL(string: urlString) else {
                throw LotteryServiceError.invalidURL
            }

            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")

            let (data, _) = try await Self.sharedURLSession.data(for: request)
            response = String(data: data, encoding: .utf8)
        }

        guard let json = response else {
            throw LotteryServiceError.noData
        }

        do {
            // Parse the outer JSON response
            guard let jsonData = json.data(using: .utf8),
                  let outerJson = try JSONSerialization.jsonObject(with: jsonData) as? [String: Any],
                  let innerJsonString = outerJson["d"] as? String else {
                throw LotteryServiceError.parsingError
            }

            // Parse the inner JSON string
            guard let innerJsonData = innerJsonString.data(using: .utf8),
                  let innerJson = try JSONSerialization.jsonObject(with: innerJsonData) as? [String: Any] else {
                throw LotteryServiceError.parsingError
            }

            // Extract jackpot information
            guard let jackpot = innerJson["Jackpot"] as? [String: Any],
                  let nextPrizePool = jackpot["NextPrizePool"] as? Double,
                  let nextCashValue = jackpot["NextCashValue"] as? Double else {
                throw LotteryServiceError.parsingError
            }

            // Extract drawing information
            guard let drawing = innerJson["Drawing"] as? [String: Any],
                  let playDateString = drawing["UpdatedTime"] as? String,
                  let n1 = drawing["N1"] as? Int,
                  let n2 = drawing["N2"] as? Int,
                  let n3 = drawing["N3"] as? Int,
                  let n4 = drawing["N4"] as? Int,
                  let n5 = drawing["N5"] as? Int,
                  let mBall = drawing["MBall"] as? Int else {
                throw LotteryServiceError.parsingError
            }

            // Extract next drawing date
            guard let nextDrawingDateString = innerJson["NextDrawingDate"] as? String else {
                throw LotteryServiceError.parsingError
            }

            guard let drawingDate = parseDrawingDate(from: playDateString),
                  let nextDrawingDate = parseDrawingDate(from: nextDrawingDateString) else {
                throw LotteryServiceError.parsingError
            }
            
            let winningNumbers = [n1, n2, n3, n4, n5]

            // Extract winners information
            let winners = extractWinners(from: innerJson)

            // Format the jackpot and cash value as strings
            let estimatedJackpot = DataFormatter.formatCurrency(nextPrizePool)
            let cashValue = DataFormatter.formatCurrency(nextCashValue, isCash: true)

            return DrawingInfo(
                jackpot: estimatedJackpot,
                cash: cashValue,
                drawingDate: drawingDate,
                nextDrawingDate: nextDrawingDate,
                winningNumbers: winningNumbers,
                theBall: mBall,
                winners: winners,
                lotteryType: .megaMillions
            )

        } catch {
            throw LotteryServiceError.parsingError
        }
    }

    private func parseDrawingDate(from dateString: String) -> Date? {
        // Expected format: "2025-07-01T23:00:00"
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
        formatter.timeZone = TimeZone(identifier: "America/New_York") // Eastern Time

        return formatter.date(from: dateString)
    }
    
    private func extractWinners(from json: [String: Any]) -> [Winner] {
        var winners: [Winner] = []
        
        // Extract winners from MatchWinners
        if let matchWinners = json["MatchWinners"] as? [String: Any],
           let rawText = matchWinners["RawText"] as? String {
            
            // Parse the raw text to extract state information
            if rawText.contains("Jackpot Winner:") {
                // Format: "Match 5 + 1 Jackpot Winner: VA "
                let components = rawText.components(separatedBy: ":")
                if components.count > 1 {
                    let stateInfo = components[1].trimmingCharacters(in: .whitespaces)
                    if !stateInfo.isEmpty {
                        let winner = Winner(
                            label: stateInfo,
                            amount: "Jackpot",
                            type: .jackpot
                        )
                        winners.append(winner)
                    }
                }
            } else if rawText.contains("Match 5 + 0:") {
                // Format: "Match 5 + 0: GA "
                let components = rawText.components(separatedBy: ":")
                if components.count > 1 {
                    let stateInfo = components[1].trimmingCharacters(in: .whitespaces)
                    if !stateInfo.isEmpty {
                        let winner = Winner(
                            label: stateInfo,
                            amount: "$1 Million",
                            type: .match5
                        )
                        winners.append(winner)
                    }
                }
            }
        }
        
        return winners
    }
}
