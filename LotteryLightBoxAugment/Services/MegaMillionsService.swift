//
//  MegaMillionsService.swift
//  LotteryLightBoxAugment
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/8/25.
//

import Foundation

class MegaMillionsService: LotteryServiceProtocol {
    var urlString = "https://www.megamillions.com/cmspages/utilservice.asmx/GetLatestDrawData"
    var response: String?
    
    init(response: String? = nil) {
        self.response = response
    }
    
    func fetchNextDrawingInfo() async throws -> DrawingInfo {
        if response == nil {
            guard let url = URL(string: urlString) else {
                throw LotteryServiceError.invalidURL
            }

            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")

            let (data, _) = try await Self.sharedURLSession.data(for: request)
            response = String(data: data, encoding: .utf8)
        }

        guard let json = response else {
            throw LotteryServiceError.noData
        }

        do {
            // Parse the outer JSON response
            guard let jsonData = json.data(using: .utf8),
                  let outerJson = try JSONSerialization.jsonObject(with: jsonData) as? [String: Any],
                  let innerJsonString = outerJson["d"] as? String else {
                throw LotteryServiceError.parsingError
            }

            // Parse the inner JSON string
            guard let innerJsonData = innerJsonString.data(using: .utf8),
                  let innerJson = try JSONSerialization.jsonObject(with: innerJsonData) as? [String: Any] else {
                throw LotteryServiceError.parsingError
            }

            // Extract jackpot information
            guard let jackpot = innerJson["Jackpot"] as? [String: Any],
                  let nextPrizePool = jackpot["NextPrizePool"] as? Double,
                  let nextCashValue = jackpot["NextCashValue"] as? Double else {
                throw LotteryServiceError.parsingError
            }

            // Extract drawing information
            guard let drawing = innerJson["Drawing"] as? [String: Any],
                  let playDateString = drawing["UpdatedTime"] as? String,
                  let n1 = drawing["N1"] as? Int,
                  let n2 = drawing["N2"] as? Int,
                  let n3 = drawing["N3"] as? Int,
                  let n4 = drawing["N4"] as? Int,
                  let n5 = drawing["N5"] as? Int,
                  let mBall = drawing["MBall"] as? Int else {
                throw LotteryServiceError.parsingError
            }

            // Extract next drawing date (optional field)
            let nextDrawingDateString = innerJson["NextDrawingDate"] as? String

            guard let drawingDate = parseDrawingDate(from: playDateString) else {
                throw LotteryServiceError.parsingError
            }

            // If NextDrawingDate is not provided, calculate it (e.g., next Tuesday and Friday at 11 PM ET)
            let nextDrawingDate: Date
            if let nextDrawingDateString = nextDrawingDateString,
               let parsedNextDrawingDate = parseDrawingDate(from: nextDrawingDateString) {
                nextDrawingDate = parsedNextDrawingDate
            } else {
                // Default to a future date for testing purposes
                nextDrawingDate = Calendar.current.date(byAdding: .day, value: 3, to: drawingDate) ?? drawingDate
            }
            
            let winningNumbers = [n1, n2, n3, n4, n5]

            // Extract winners information
            let winners = extractWinners(from: innerJson)

            // Format the jackpot and cash value as strings
            let estimatedJackpot = DataFormatter.formatCurrency(nextPrizePool)
            let cashValue = DataFormatter.formatCurrency(nextCashValue, isCash: true)

            return DrawingInfo(
                jackpot: estimatedJackpot,
                cash: cashValue,
                drawingDate: drawingDate,
                nextDrawingDate: nextDrawingDate,
                winningNumbers: winningNumbers,
                theBall: mBall,
                winners: winners,
                lotteryType: .megaMillions
            )

        } catch {
            throw LotteryServiceError.parsingError
        }
    }

    private func parseDrawingDate(from dateString: String) -> Date? {
        // Expected format: "2025-07-01T23:00:00"
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
        formatter.timeZone = TimeZone(identifier: "America/New_York") // Eastern Time

        return formatter.date(from: dateString)
    }
    
    private func extractWinners(from json: [String: Any]) -> [Winner] {
        var winners: [Winner] = []

        // Extract winners from MatchWinners
        if let matchWinners = json["MatchWinners"] as? [String: Any],
           let rawText = matchWinners["RawText"] as? String {

            // Parse the raw text to extract state information
            if rawText.contains("Jackpot Winner:") {
                // Format: "Match 5 + 1 Jackpot Winner: VA "
                let components = rawText.components(separatedBy: ":")
                if components.count > 1 {
                    let stateInfo = components[1].trimmingCharacters(in: .whitespaces)
                    if !stateInfo.isEmpty {
                        let winner = Winner(
                            label: stateInfo,
                            amount: "Jackpot",
                            type: .jackpot
                        )
                        winners.append(winner)
                    }
                }
            } else if rawText.contains("Match 5 + 0:") {
                // Extract state information from raw text
                let components = rawText.components(separatedBy: ":")
                if components.count > 1 {
                    let stateInfo = components[1].trimmingCharacters(in: .whitespaces)
                    if !stateInfo.isEmpty {
                        // Parse states from the text (e.g., "GA, NJ, WA ")
                        let states = stateInfo.components(separatedBy: ",")
                            .map { $0.trimmingCharacters(in: .whitespaces) }
                            .filter { !$0.isEmpty }

                        // Get Tier 1 winners and their multipliers from PrizeTiers
                        let tier1Winners = extractTier1Winners(from: json)

                        // Get prize amounts for each multiplier from PrizeMatrix
                        let prizeAmounts = extractPrizeAmounts(from: json)

                        // Create combined amounts string
                        var amounts: [String] = []

                        // Sort multipliers to ensure consistent assignment
                        let sortedMultipliers = tier1Winners.keys.sorted { multiplier1, multiplier2 in
                            let value1 = extractMultiplierValue(multiplier1)
                            let value2 = extractMultiplierValue(multiplier2)
                            return value1 < value2
                        }

                        for multiplier in sortedMultipliers {
                            guard let count = tier1Winners[multiplier],
                                  let prizeAmount = prizeAmounts[multiplier] else {
                                continue
                            }

                            let formattedAmount = formatPrizeAmount(prizeAmount)

                            // Add the amount for each winner with this multiplier
                            for _ in 0..<count {
                                amounts.append(formattedAmount)
                            }
                        }

                        // Create a single winner entry with combined states and amounts
                        if !states.isEmpty && !amounts.isEmpty {
                            let combinedStates = states.joined(separator: ", ")
                            let combinedAmounts = amounts.joined(separator: ", ")

                            let winner = Winner(
                                label: combinedStates,
                                amount: combinedAmounts,
                                type: .match5
                            )
                            winners.append(winner)
                        }
                    }
                }
            }
        }

        return winners
    }

    private func extractTier1Winners(from json: [String: Any]) -> [String: Int] {
        var tier1Winners: [String: Int] = [:]

        guard let prizeTiers = json["PrizeTiers"] as? [[String: Any]] else {
            return tier1Winners
        }

        for prizeTier in prizeTiers {
            if let tier = prizeTier["Tier"] as? Int,
               let winners = prizeTier["Winners"] as? Int,
               let multiplier = prizeTier["Multiplier"] as? String,
               tier == 1 && winners > 0 {
                tier1Winners[multiplier] = winners
            }
        }

        return tier1Winners
    }

    private func extractPrizeAmounts(from json: [String: Any]) -> [String: Double] {
        var prizeAmounts: [String: Double] = [:]

        guard let prizeMatrix = json["PrizeMatrix"] as? [String: Any],
              let prizeTiers = prizeMatrix["PrizeTiers"] as? [[String: Any]] else {
            return prizeAmounts
        }

        for prizeTier in prizeTiers {
            if let tier = prizeTier["PrizeTier"] as? Int, tier == 1 {
                // Map multiplier names to their corresponding prize amounts
                if let mega2 = prizeTier["Mega2"] as? Double {
                    prizeAmounts["2x"] = mega2
                }
                if let mega3 = prizeTier["Mega3"] as? Double {
                    prizeAmounts["3x"] = mega3
                }
                if let mega4 = prizeTier["Mega4"] as? Double {
                    prizeAmounts["4x"] = mega4
                }
                if let mega5 = prizeTier["Mega5"] as? Double {
                    prizeAmounts["5x"] = mega5
                }
                if let mega10 = prizeTier["Mega10"] as? Double {
                    prizeAmounts["10x"] = mega10
                }
                break
            }
        }

        return prizeAmounts
    }

    private func formatPrizeAmount(_ amount: Double) -> String {
        return DataFormatter.formatCurrency(amount)
    }

    private func extractMultiplierValue(_ multiplier: String) -> Int {
        // Extract numeric value from multiplier string (e.g., "2x" -> 2)
        let numericString = multiplier.replacingOccurrences(of: "x", with: "")
        return Int(numericString) ?? 0
    }
}
