//
//  LotteryServiceProtocol.swift
//  LotteryLightBoxAugment
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/8/25.
//

import Foundation

enum LotteryServiceError: Error, Equatable {
    case invalidURL
    case noData
    case parsingError
    case networkError(Error)
    
    static func == (lhs: LotteryServiceError, rhs: LotteryServiceError) -> Bo<PERSON> {
        switch (lhs, rhs) {
        case (.invalidURL, .invalidURL):
            return true
        case (.noData, .noData):
            return true
        case (.parsingError, .parsingError):
            return true
        case (.networkError(let lhsError), .networkError(let rhsError)):
            return lhsError.localizedDescription == rhsError.localizedDescription
        default:
            return false
        }
    }
}

protocol LotteryServiceProtocol: AnyObject {
    func fetchNextDrawingInfo() async throws -> DrawingInfo
}

extension LotteryServiceProtocol {
    /// Shared URLSession with extended timeouts for lottery data fetching
    static var sharedURLSession: URLSession {
        let configuration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = 30.0  // 30 seconds for request timeout
        configuration.timeoutIntervalForResource = 60.0 // 60 seconds for total resource timeout
        configuration.requestCachePolicy = .reloadIgnoringLocalAndRemoteCacheData // Always fetch fresh data
        return URLSession(configuration: configuration)
    }
}
