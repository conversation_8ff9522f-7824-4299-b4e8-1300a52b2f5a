//
//  BackgroundTaskManager.swift
//  LotteryLightBoxAugment
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/8/25.
//

import Foundation
import BackgroundTasks
import WidgetKit
import UIKit

class BackgroundTaskManager {
    static let shared = BackgroundTaskManager()
    
    private let taskIdentifier = "com.koo.jaesung.LotteryLightbox.refresh"
    private let dataManager = LotteryDataManager.shared
    
    private init() {}
    
    // MARK: - Background Task Scheduling
    
    func scheduleBackgroundRefresh() {
        // Check background app refresh status
        let backgroundRefreshStatus = UIApplication.shared.backgroundRefreshStatus
        print("Background app refresh status: \(backgroundRefreshStatus.rawValue)")
        
        guard backgroundRefreshStatus == .available else {
            print("Background app refresh not available - status: \(backgroundRefreshStatus)")
            return
        }
        
        // Cancel any existing tasks first
        BGTaskScheduler.shared.cancel(taskRequestWithIdentifier: taskIdentifier)
        
        let request = BGAppRefreshTaskRequest(identifier: taskIdentifier)
        
        // Calculate next refresh time based on drawing dates
        let nextRefreshDate = calculateNextRefreshDate()
        request.earliestBeginDate = nextRefreshDate
        
        print("Attempting to schedule background task for: \(nextRefreshDate)")
        
        do {
            try BGTaskScheduler.shared.submit(request)
            print("✅ Background refresh scheduled successfully for: \(nextRefreshDate)")
        } catch {
            let errorMessage = handleBackgroundTaskError(error)
            print("❌ Failed to schedule background refresh: \(errorMessage)")
        }
    }
    
    private func handleBackgroundTaskError(_ error: Error) -> String {
        if let bgError = error as? BGTaskScheduler.Error {
            switch bgError.code {
            case .unavailable:
                return "Background task scheduler unavailable - check Background App Refresh settings"
            case .tooManyPendingTaskRequests:
                return "Too many pending background tasks"
            case .notPermitted:
                return "Background processing not permitted"
            default:
                return "Background task error: \(bgError.localizedDescription)"
            }
        }
        return error.localizedDescription
    }
    
    // MARK: - Debug Methods
    
    func debugBackgroundTaskStatus() {
        print("=== Background Task Debug Info ===")
        print("Task identifier: \(taskIdentifier)")
        print("App background refresh status: \(UIApplication.shared.backgroundRefreshStatus.rawValue)")
        
        switch UIApplication.shared.backgroundRefreshStatus {
        case .available:
            print("✅ Background App Refresh: Available")
        case .denied:
            print("❌ Background App Refresh: Denied - Enable in Settings > General > Background App Refresh")
        case .restricted:
            print("⚠️ Background App Refresh: Restricted by parental controls or device management")
        @unknown default:
            print("❓ Background App Refresh: Unknown status")
        }
    }
    
    // MARK: - Background Task Execution
    
    func handleBackgroundRefresh() async {
        print("Background refresh task started")
        
        // Schedule the next refresh
        scheduleBackgroundRefresh()
        
        do {
            await refreshExpiredData()
            
            // Notify widgets to refresh using the widget update manager
            WidgetUpdateManager.shared.handleDataRefreshCompleted()
            
            print("Background refresh completed successfully")
        } catch {
            print("Background refresh failed: \(error)")
        }
    }
    
    // MARK: - Data Refresh Logic
    
    private func refreshExpiredData() async {
        print("Checking for expired lottery data in background...")
        
        // Check which lotteries have expired data
        var expiredLotteries: [LotteryType] = []
        
        for lotteryType in LotteryType.allCases {
            if dataManager.isDataExpired(for: lotteryType) {
                expiredLotteries.append(lotteryType)
            }
        }
        
        if !expiredLotteries.isEmpty {
            print("Found expired data for: \(expiredLotteries.map { $0.rawValue })")
            
            // Fetch new data for expired lotteries
            await withTaskGroup(of: Void.self) { group in
                for lotteryType in expiredLotteries {
                    group.addTask {
                        do {
                            try await self.dataManager.refreshData(for: lotteryType)
                            print("Successfully refreshed \(lotteryType) in background")
                        } catch {
                            print("Failed to refresh \(lotteryType) in background: \(error)")
                        }
                    }
                }
            }
        } else {
            print("No expired data found in background check")
        }
    }
    
    // MARK: - Helper Methods
    
    private func calculateNextRefreshDate() -> Date {
        let currentDate = Date()
        var earliestNextDraw = Date.distantFuture
        
        // Find the earliest upcoming drawing date
        for lotteryType in LotteryType.allCases {
            if let nextDrawDate = dataManager.getNextDrawingDate(for: lotteryType) {
                if nextDrawDate < earliestNextDraw {
                    earliestNextDraw = nextDrawDate
                }
            }
        }
        
        // If we have a valid next drawing date, schedule refresh 30 minutes after
        if earliestNextDraw != Date.distantFuture && earliestNextDraw > currentDate {
            return Calendar.current.date(byAdding: .minute, value: 30, to: earliestNextDraw) ?? currentDate.addingTimeInterval(30 * 60)
        }
        
        // Default: refresh in 30 minutes
        return currentDate.addingTimeInterval(30 * 60)
    }
    
    // MARK: - App Lifecycle Methods
    
    func handleAppDidEnterBackground() {
        // When app enters background, schedule refresh if needed
        print("App entered background, scheduling background refresh...")
        debugBackgroundTaskStatus()
        scheduleBackgroundRefresh()
    }
    
    func handleAppWillEnterForeground() async {
        // When app enters foreground, check if we need to refresh data
        print("App entering foreground, checking for data updates...")
        
        // Check and refresh data if needed
        await dataManager.checkAndRefreshData()
        
        // Use the widget update manager for organized updates
        WidgetUpdateManager.shared.handleAppWillEnterForeground()
    }
    

} 
