//
//  LotteryDataManager.swift
//  LotteryLightBoxAugment
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/8/25.
//

import Foundation

class LotteryDataManager: ObservableObject {
    static let shared = LotteryDataManager()
    
    private let appGroupIdentifier = "group.koo.jaesung.LotteryLightbox"
    
    @Published var lotteryData: [LotteryType: DrawingInfo] = [:]
    
    private init() {}
    
    // MARK: - Helper Methods
    
    private func storageKey(for lotteryType: LotteryType) -> String {
        switch lotteryType {
        case .powerball:
            return "powerball_data"
        case .megaMillions:
            return "megamillions_data"
        case .superLottoPlus:
            return "superlottoplus_data"
        }
    }
    
    private func lastUpdateKey(for lotteryType: LotteryType) -> String {
        switch lotteryType {
        case .powerball:
            return "powerball_last_update"
        case .megaMillions:
            return "megamillions_last_update"
        case .superLottoPlus:
            return "superlottoplus_last_update"
        }
    }
    
    private func service(for lotteryType: LotteryType) -> LotteryServiceProtocol {
        switch lotteryType {
        case .powerball:
            return PowerballService()
        case .megaMillions:
            return MegaMillionsService()
        case .superLottoPlus:
            return SuperLottoPlusService()
        }
    }
    
    // MARK: - Data Storage
    
    func saveData(_ data: DrawingInfo, for lotteryType: LotteryType) {
        lotteryData[lotteryType] = data
        saveToUserDefaults(data, forType: lotteryType)
        saveLastUpdateTime(for: lotteryType)
    }
    
    private func saveToUserDefaults(_ data: DrawingInfo, forType lotteryType: LotteryType) {
        let key = storageKey(for: lotteryType)
        guard let userDefaults = UserDefaults(suiteName: appGroupIdentifier) else { 
            print("Main app: Failed to create UserDefaults with app group identifier: \(appGroupIdentifier)")
            return 
        }
        
        do {
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let encodedData = try encoder.encode(data)
            userDefaults.set(encodedData, forKey: key)
            print("Main app: Successfully saved data for \(lotteryType): \(encodedData.count) bytes")
        } catch {
            print("Main app: Failed to save lottery data for \(lotteryType): \(error)")
        }
    }
    
    private func saveLastUpdateTime(for lotteryType: LotteryType) {
        let key = lastUpdateKey(for: lotteryType)
        guard let userDefaults = UserDefaults(suiteName: appGroupIdentifier) else { 
            print("Main app: Failed to create UserDefaults for last update time")
            return 
        }
        
        let currentTime = Date()
        userDefaults.set(currentTime, forKey: key)
        print("Main app: Saved last update time for \(lotteryType): \(currentTime)")
    }
    
    // MARK: - Data Retrieval
    
    func loadData(for lotteryType: LotteryType) -> DrawingInfo? {
        let key = storageKey(for: lotteryType)
        guard let userDefaults = UserDefaults(suiteName: appGroupIdentifier) else { 
            print("Main app: Failed to create UserDefaults with app group identifier: \(appGroupIdentifier)")
            return nil 
        }
        
        guard let data = userDefaults.data(forKey: key) else { 
            print("Main app: No data found for \(lotteryType)")
            return nil 
        }
        
        print("Main app: Found data for \(lotteryType): \(data.count) bytes")
        
        do {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            let result = try decoder.decode(DrawingInfo.self, from: data)
            print("Main app: Successfully decoded data for \(lotteryType)")
            return result
        } catch {
            print("Main app: Failed to load lottery data for \(lotteryType): \(error)")
            return nil
        }
    }
    
    // MARK: - Data Validation
    
    private func isDataExpired(_ drawingInfo: DrawingInfo) -> Bool {
        return Date() > drawingInfo.nextDrawingDate
    }
    
    private func hasNoData() -> Bool {
        return lotteryData.isEmpty
    }
    
    private func hasAnyMissingData() -> Bool {
        return LotteryType.allCases.contains { lotteryType in
            lotteryData[lotteryType] == nil
        }
    }
    
    func loadAllData() {
        print("Main app: Loading all lottery data...")
        
        // First, load existing data from UserDefaults
        for lotteryType in LotteryType.allCases {
            if let data = loadData(for: lotteryType) {
                lotteryData[lotteryType] = data
            }
        }
        
        // Check if we need to refresh data
        Task {
            await checkAndRefreshData()
        }
    }
    
    func checkAndRefreshData() async {
        // If no data exists at all (first launch), refresh everything
        if hasNoData() || hasAnyMissingData() {
            print("Main app: No data or missing data detected, refreshing all data...")
            await refreshAllData()
            return
        }
        
        // Check each lottery for expired drawing dates
        var expiredLotteries: [LotteryType] = []
        
        for lotteryType in LotteryType.allCases {
            if let data = lotteryData[lotteryType], isDataExpired(data) {
                expiredLotteries.append(lotteryType)
            }
        }
        
        if !expiredLotteries.isEmpty {
            print("Main app: Found expired data for lotteries: \(expiredLotteries.map { $0.rawValue })")
            await refreshExpiredLotteries(expiredLotteries)
        } else {
            print("Main app: All lottery data is up to date")
        }
    }
    
    private func refreshExpiredLotteries(_ lotteryTypes: [LotteryType]) async {
        print("Main app: Refreshing expired lottery data...")
        
        await withTaskGroup(of: Result<LotteryType, Error>.self) { group in
            for lotteryType in lotteryTypes {
                group.addTask {
                    do {
                        try await self.refreshData(for: lotteryType)
                        return .success(lotteryType)
                    } catch {
                        return .failure(error)
                    }
                }
            }
            
            // Wait for all tasks to complete
            var completedTasks = 0
            let totalTasks = lotteryTypes.count
            
            for await result in group {
                completedTasks += 1
                switch result {
                case .success(let lotteryType):
                    print("Main app: Successfully refreshed expired \(lotteryType) data (\(completedTasks)/\(totalTasks))")
                case .failure(let error):
                    print("Main app: Failed to refresh expired lottery data: \(error)")
                }
            }
            
            print("Main app: Completed refresh of expired data (\(completedTasks)/\(totalTasks) tasks finished)")
        }
    }

    // MARK: - Data Refresh
    
    func refreshAllData() async {
        print("Main app: Starting refresh of all data...")
        
        let allLotteryTypes = LotteryType.allCases
        
        // Use Result to capture success/failure without cancellation propagation
        await withTaskGroup(of: Result<LotteryType, Error>.self) { group in
            for lotteryType in allLotteryTypes {
                group.addTask {
                    do {
                        try await self.refreshData(for: lotteryType)
                        return .success(lotteryType)
                    } catch {
                        return .failure(error)
                    }
                }
            }
            
            // Wait for all tasks to complete
            var completedTasks = 0
            let totalTasks = allLotteryTypes.count
            
            for await result in group {
                completedTasks += 1
                switch result {
                case .success(let lotteryType):
                    print("Main app: Successfully refreshed \(lotteryType) data (\(completedTasks)/\(totalTasks))")
                case .failure(let error):
                    print("Main app: Failed to refresh lottery data: \(error)")
                }
            }
            
            print("Main app: Completed refresh of all data (\(completedTasks)/\(totalTasks) tasks finished)")
        }
    }
    
    func refreshData(for lotteryType: LotteryType) async throws {
        let service = service(for: lotteryType)
        print("Main app: Fetching real \(lotteryType) data...")
        let data = try await service.fetchNextDrawingInfo()
        await MainActor.run {
            print("Main app: Successfully fetched real \(lotteryType) data")
            self.saveData(data, for: lotteryType)
        }
    }
    
    // MARK: - Background-specific methods
    
    func getNextDrawingDate(for lotteryType: LotteryType) -> Date? {
        return lotteryData[lotteryType]?.nextDrawingDate ?? loadData(for: lotteryType)?.nextDrawingDate
    }
    
    func isDataExpired(for lotteryType: LotteryType) -> Bool {
        guard let data = lotteryData[lotteryType] ?? loadData(for: lotteryType) else {
            return true // No data means expired
        }
        return Date() > data.nextDrawingDate
    }
    
    func getLastUpdateTime(for lotteryType: LotteryType) -> Date? {
        let key = lastUpdateKey(for: lotteryType)
        guard let userDefaults = UserDefaults(suiteName: appGroupIdentifier) else { 
            print("Main app: Failed to create UserDefaults for last update time retrieval")
            return nil 
        }
        
        return userDefaults.object(forKey: key) as? Date
    }
    
    func hasRecentDataUpdate(for lotteryType: LotteryType, within timeInterval: TimeInterval) -> Bool {
        guard let lastUpdate = getLastUpdateTime(for: lotteryType) else {
            return false
        }
        
        let timeSinceUpdate = Date().timeIntervalSince(lastUpdate)
        return timeSinceUpdate < timeInterval
    }
    
    func isDataStale(for lotteryType: LotteryType, olderThan timeInterval: TimeInterval = 30 * 60) -> Bool {
        guard let lastUpdate = getLastUpdateTime(for: lotteryType) else {
            return true // No update time recorded means stale
        }
        
        let timeSinceUpdate = Date().timeIntervalSince(lastUpdate)
        return timeSinceUpdate > timeInterval
    }
    
    func hasAnyStaleData(olderThan timeInterval: TimeInterval = 30 * 60) -> Bool {
        return LotteryType.allCases.contains { lotteryType in
            isDataStale(for: lotteryType, olderThan: timeInterval)
        }
    }
} 
