//
//  SuperLottoPlusService.swift
//  LotteryLightBoxAugment
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/22/25.
//

import Foundation

// JSON Response structures for CA Lottery API
struct SuperLottoPlusAPIResponse: Codable {
    let DrawGameId: Int
    let Name: String
    let NextDraw: NextDrawInfo?
    let MostRecentDraw: RecentDrawInfo?
    let HasJackpot: Bool
}

struct NextDrawInfo: Codable {
    let DrawNumber: Int
    let DrawDate: String
    let DrawCloseTime: String?
    let JackpotAmount: Int?
    let EstimatedCashValue: Int?
    let DrawCloseDateTime: String?
}

struct RecentDrawInfo: Codable {
    let DrawNumber: Int
    let DrawDate: String
    let WinningNumbers: [String: WinningNumber]
    let Prizes: [String: Prize]?
}

struct WinningNumber: Codable {
    let Number: String
    let IsSpecial: Bool
    let Name: String?
}

struct Prize: Codable {
    let PrizeTypeDescription: String
    let Count: Int
    let Amount: Int
}

class SuperLottoPlusService: LotteryServiceProtocol {
    var urlString = "https://www.calottery.com/api/DrawGameApi/DrawGamePastDrawResults/8/1/20"
    var response: String?
    
    init(response: String? = nil) {
        self.response = response
    }
    
    func fetchNextDrawingInfo() async throws -> DrawingInfo {
        if response == nil {
            guard let url = URL(string: urlString) else {
                throw LotteryServiceError.invalidURL
            }
            let (data, _) = try await Self.sharedURLSession.data(from: url)
            response = String(data: data, encoding: .utf8)
        }
        
        guard let jsonString = response else {
            throw LotteryServiceError.noData
        }
        
        guard let jsonData = jsonString.data(using: .utf8) else {
            throw LotteryServiceError.parsingError
        }
        
        let decoder = JSONDecoder()
        let apiResponse: SuperLottoPlusAPIResponse
        
        do {
            apiResponse = try decoder.decode(SuperLottoPlusAPIResponse.self, from: jsonData)
        } catch {
            throw LotteryServiceError.parsingError
        }
        
        // Extract jackpot amount
        let jackpotAmount = extractJackpotAmount(from: apiResponse)
        
        // Extract cash value
        let cashValue = extractCashValue(from: apiResponse)
        
        // Extract winning numbers from most recent draw
        let (winningNumbers, megaNumber) = extractWinningNumbers(from: apiResponse)
        
        // Extract drawing dates
        let drawingDate = extractDrawingDate(from: apiResponse)
        let nextDrawingDate = extractNextDrawingDate(from: apiResponse)
        
        // Extract winners from most recent draw
        let winners = extractWinners(from: apiResponse)
        
        return DrawingInfo(
            jackpot: jackpotAmount,
            cash: cashValue,
            drawingDate: drawingDate,
            nextDrawingDate: nextDrawingDate,
            winningNumbers: winningNumbers,
            theBall: megaNumber,
            winners: winners,
            lotteryType: .superLottoPlus
        )
    }
    
    private func extractWinners(from response: SuperLottoPlusAPIResponse) -> [Winner] {
        var winners: [Winner] = []
        
        guard let recentDraw = response.MostRecentDraw,
              let prizes = recentDraw.Prizes else {
            return winners
        }
        
        // Iterate through all prize entries
        for (_, prize) in prizes {
            let description = prize.PrizeTypeDescription
            let count = prize.Count
            let amount = prize.Amount
            
            // Check for jackpot winners (5 + Mega)
            if description == "5 + Mega" && count > 0 {
                let formattedAmount = DataFormatter.formatCurrency(Double(amount))
                for _ in 0..<count {
                    winners.append(Winner(label: "Mathch 5 + Mega", amount: formattedAmount, type: .jackpot))
                }
            }
            
            // Check for Match 5 winners (5 numbers without Mega)
            if description == "5" && count > 0 {
                let formattedAmount = DataFormatter.formatCurrency(Double(amount), isCash: true)
                for _ in 0..<count {
                    winners.append(Winner(label: "Match 5", amount: formattedAmount, type: .match5))
                }
            }
        }
        
        return winners
    }
    
    private func extractJackpotAmount(from response: SuperLottoPlusAPIResponse) -> String {
        guard let jackpotAmount = response.NextDraw?.JackpotAmount else {
            return "Unknown"
        }
        
        return DataFormatter.formatCurrency(Double(jackpotAmount))
    }
    
    private func extractCashValue(from response: SuperLottoPlusAPIResponse) -> String {
        guard let cashValue = response.NextDraw?.EstimatedCashValue else {
            return "Unknown"
        }
        
        return DataFormatter.formatCurrency(Double(cashValue), isCash: true)
    }
    
    private func extractWinningNumbers(from response: SuperLottoPlusAPIResponse) -> ([Int], Int) {
        guard let recentDraw = response.MostRecentDraw else {
            return ([], 0)
        }
        
        var regularNumbers: [Int] = []
        var specialNumber: Int = 0
        
        // Sort by key to maintain order (0, 1, 2, 3, 4, 5)
        let sortedNumbers = recentDraw.WinningNumbers.sorted { 
            guard let key1 = Int($0.key), let key2 = Int($1.key) else { return false }
            return key1 < key2
        }
        
        for (_, winningNumber) in sortedNumbers {
            if let number = Int(winningNumber.Number) {
                if winningNumber.IsSpecial {
                    specialNumber = number
                } else {
                    regularNumbers.append(number)
                }
            }
        }
        
        return (regularNumbers, specialNumber)
    }
    
    private func extractDrawingDate(from response: SuperLottoPlusAPIResponse) -> Date {
        guard let recentDraw = response.MostRecentDraw else {
            return Date()
        }
        
        return parseAPIDate(from: recentDraw.DrawDate) ?? Date()
    }
    
    private func extractNextDrawingDate(from response: SuperLottoPlusAPIResponse) -> Date {
        guard let nextDraw = response.NextDraw else {
            return Date()
        }
        
        return parseAPIDate(from: nextDraw.DrawDate) ?? Date()
    }
    
    private func parseAPIDate(from dateString: String) -> Date? {
        // Parse ISO 8601 format like "2025-07-19T07:00:00"
        let isoFormatter = ISO8601DateFormatter()
        isoFormatter.formatOptions = [.withInternetDateTime]
        
        var parsedDate: Date?
        
        // Try ISO8601 formatter first
        if let date = isoFormatter.date(from: dateString) {
            parsedDate = date
        } else {
            // Fallback to regular DateFormatter for formats like "2025-07-19T07:00:00"
            let fallbackFormatter = DateFormatter()
            fallbackFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
            fallbackFormatter.timeZone = TimeZone(secondsFromGMT: 0) // UTC
            parsedDate = fallbackFormatter.date(from: dateString)
        }
        
        guard let date = parsedDate else {
            return nil
        }
        
        // Convert to Pacific Time for CA Lottery drawing time (7:57 PM)
        var calendar = Calendar(identifier: .gregorian)
        guard let pacificTimeZone = TimeZone(identifier: "America/Los_Angeles") else {
            return date
        }
        calendar.timeZone = pacificTimeZone
        
        let components = calendar.dateComponents([.year, .month, .day], from: date)
        var drawingComponents = DateComponents()
        drawingComponents.year = components.year
        drawingComponents.month = components.month
        drawingComponents.day = components.day
        drawingComponents.hour = 19  // 7 PM
        drawingComponents.minute = 57 // 57 minutes
        drawingComponents.second = 0
        drawingComponents.timeZone = pacificTimeZone
        
        return calendar.date(from: drawingComponents)
    }
}

