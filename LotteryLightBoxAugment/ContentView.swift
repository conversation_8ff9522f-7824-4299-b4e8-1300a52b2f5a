//
//  ContentView.swift
//  LotteryLightBoxAugment
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/8/25.
//

import SwiftUI
import WidgetKit

struct ContentView: View {
    @StateObject private var dataManager = LotteryDataManager.shared
    private let backgroundTaskManager = BackgroundTaskManager.shared
    @State private var isRefreshing = false
    @State private var refreshTrigger = UUID()
    
    var body: some View {
        GeometryReader { geometry in
            ScrollView {
                LazyVGrid(columns: adaptiveColumns(for: geometry.size), spacing: 16) {
                    ForEach(LotteryType.allCases, id: \.self) { lotteryType in
                        if let lotteryInfo = dataManager.lotteryData[lotteryType] {
                            LotteryCardView(lotteryInfo)
                        } else {
                            ErrorCardView(lotteryType: lotteryType)
                        }
                    }
                }
                .padding()
            }
            .id(refreshTrigger) // Force complete view refresh when this changes
            .refreshable {
                await hardRefresh()
            }
            .background(Color.black)
            .preferredColorScheme(.dark)
            .onAppear {
                loadData()
                backgroundTaskManager.debugBackgroundTaskStatus()
            }
            .onReceive(NotificationCenter.default.publisher(for: UIApplication.didEnterBackgroundNotification)) { _ in
                backgroundTaskManager.handleAppDidEnterBackground()
            }
            .onReceive(NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)) { _ in
                Task {
                    await backgroundTaskManager.handleAppWillEnterForeground()
                }
            }
        }
    }
    
    private func adaptiveColumns(for size: CGSize) -> [GridItem] {
        let isLandscape = size.width > size.height
        let minCardWidth: CGFloat = 300
        let spacing: CGFloat = 16
        let horizontalPadding: CGFloat = 32 // Total padding (16 * 2)
        
        if isLandscape {
            // In landscape, calculate how many cards can fit
            let availableWidth = size.width - horizontalPadding
            let columnsCount = max(1, Int(availableWidth / (minCardWidth + spacing)))
            return Array(repeating: GridItem(.flexible(), spacing: spacing, alignment: .top), count: columnsCount)
        } else {
            // In portrait, use single column
            return [GridItem(.flexible(), alignment: .top)]
        }
    }
    
    private func loadData() {
        dataManager.loadAllData()
    }
    
    private func hardRefresh() async {
        // Prevent multiple simultaneous refreshes
        guard !isRefreshing else { return }
        isRefreshing = true
        defer { isRefreshing = false }
        
        print("Pull-to-refresh triggered: Starting hard refresh from remote sources...")
        
        // Use Task.detached to run completely independently of the refresh control
        await withCheckedContinuation { continuation in
            Task.detached {
                await withTaskGroup(of: Result<LotteryType, Error>.self) { group in
                    for lotteryType in LotteryType.allCases {
                        group.addTask {
                            do {
                                let service = self.createService(for: lotteryType)
                                print("Pull-to-refresh: Fetching \(lotteryType) data...")
                                let data = try await service.fetchNextDrawingInfo()
                                
                                await MainActor.run {
                                    print("Pull-to-refresh: Successfully fetched \(lotteryType) data")
                                    self.dataManager.saveData(data, for: lotteryType)
                                }
                                
                                return .success(lotteryType)
                            } catch {
                                print("Pull-to-refresh: Failed to fetch \(lotteryType): \(error)")
                                return .failure(error)
                            }
                        }
                    }
                    
                    var completedTasks = 0
                    let totalTasks = LotteryType.allCases.count
                    
                    for await result in group {
                        completedTasks += 1
                        switch result {
                        case .success(let lotteryType):
                            print("Pull-to-refresh: Successfully refreshed \(lotteryType) data (\(completedTasks)/\(totalTasks))")
                        case .failure(let error):
                            print("Pull-to-refresh: Failed to refresh: \(error)")
                        }
                    }
                    
                    print("Pull-to-refresh completed: Hard refresh finished (\(completedTasks)/\(totalTasks) tasks completed)")
                    
                    // Force view redraw after all data is fetched
                    await MainActor.run {
                        print("Pull-to-refresh: Triggering view redraw...")
                        self.dataManager.objectWillChange.send()
                        self.refreshTrigger = UUID() // Force complete view redraw
                        
                        // Trigger widget updates after successful data refresh
                        print("Pull-to-refresh: Updating widgets...")
                        WidgetUpdateManager.shared.handleDataRefreshCompleted()
                    }
                }
                
                continuation.resume()
            }
        }
    }
    
    private func createService(for lotteryType: LotteryType) -> LotteryServiceProtocol {
        switch lotteryType {
        case .powerball:
            return PowerballService()
        case .megaMillions:
            return MegaMillionsService()
        case .superLottoPlus:
            return SuperLottoPlusService()
        }
    }
    
    private func refreshDataWithRetry(for lotteryType: LotteryType, maxRetries: Int = 2) async throws {
        var lastError: Error?
        
        for attempt in 0...maxRetries {
            do {
                try await dataManager.refreshData(for: lotteryType)
                return // Success, exit retry loop
            } catch {
                lastError = error
                
                // Check if it's a cancellation error and retry
                if let urlError = error as? URLError, urlError.code == .cancelled {
                    if attempt < maxRetries {
                        print("Pull-to-refresh: Request cancelled for \(lotteryType), retrying (attempt \(attempt + 1)/\(maxRetries + 1))")
                        // Wait a bit before retrying
                        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
                        continue
                    }
                }
                
                // For non-cancellation errors, don't retry
                throw error
            }
        }
        
        if let error = lastError {
            throw error
        }
    }
}

#Preview {
    ContentView()
}
