//
//  LotteryLightBoxAugmentApp.swift
//  LotteryLightBoxAugment
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/8/25.
//

import SwiftUI
import WidgetKit

@main
struct LotteryLightBoxAugmentApp: App {
    private let backgroundTaskManager = BackgroundTaskManager.shared
    @Environment(\.scenePhase) private var scenePhase
    
    var body: some Scene {
        WindowGroup {
            ContentView()
            #if os(macOS)
                .frame(minWidth: 300, minHeight: 500)
            #endif
        }
        .backgroundTask(.appRefresh("com.koo.jaesung.LotteryLightbox.refresh")) {
            await backgroundTaskManager.handleBackgroundRefresh()
        }
        .onChange(of: scenePhase) { _, newPhase in
            switch newPhase {
            case .active:
                // App became active - trigger widget updates
                Task {
                    await handleAppBecameActive()
                }
            case .background:
                // App entered background
                backgroundTaskManager.handleAppDidEnterBackground()
            case .inactive:
                break
            @unknown default:
                break
            }
        }
    }
    
    @MainActor
    private func handleAppBecameActive() async {
        // When app becomes active, refresh data and update widgets
        await backgroundTaskManager.handleAppWillEnterForeground()
        
        // Use the widget update manager for organized widget updates
        WidgetUpdateManager.shared.handleAppBecameActive()
        
        print("App became active - triggered widget updates")
    }
}
