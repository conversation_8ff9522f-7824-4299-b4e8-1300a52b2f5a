//
//  SharedDataModels.swift
//  LotteryLightBoxAugment
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/8/25.
//

import Foundation

enum LotteryType: String, CaseIterable, Codable {
    case powerball = "Powerball"
    case megaMillions = "Mega Millions"
    case superLottoPlus = "SuperLotto Plus"
}

struct DrawingInfo: Codable, Hashable {
    let jackpot: String
    let cash: String
    let drawingDate: Date
    let nextDrawingDate: Date
    let winningNumbers: [Int]
    let theBall: Int
    let winners: [Winner]
    let lotteryType: LotteryType
    
    init(jackpot: String, cash: String, drawingDate: Date, nextDrawingDate: Date, winningNumbers: [Int], theBall: Int, winners: [Winner] = [], lotteryType: LotteryType) {
        self.jackpot = jackpot
        self.cash = cash
        self.drawingDate = drawingDate
        self.nextDrawingDate = nextDrawingDate
        self.winningNumbers = winningNumbers
        self.theBall = theBall
        self.winners = winners
        self.lotteryType = lotteryType
    }
}

enum WinnerType: Codable {
    case jackpot
    case match5
}

struct Winner: Codable, <PERSON>hable {
    let label: String
    let amount: String
    let type: WinnerType
} 
