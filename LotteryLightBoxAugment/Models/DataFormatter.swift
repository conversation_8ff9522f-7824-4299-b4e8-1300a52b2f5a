//
//  DataFormatter.swift
//  LotteryLightBoxAugment
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/11/25.
//

import Foundation

struct DataFormatter {
    
    // MARK: - Jackpot Formatting
    
    static func formatWidgetJackpot(_ jackpot: String) -> String {
        return jackpot
            .replacingOccurrences(of: "$", with: "")
            .replacingOccurrences(of: " Million", with: "")
            .replacingOccurrences(of: " Billion", with: "B")
    }
    
    // MARK: - Cash Formatting
    
    static func formatWidgetCash(_ cash: String) -> String {
        if cash.contains("Billion") {
            return cash.replacingOccurrences(of: " Billion", with: "B")
        } else if cash.contains("Million") {
            return cash.replacingOccurrences(of: " Million", with: "M")
        }
        return cash
    }
    
    // MARK: - Format Currency
    
    static func formatCurrency(_ amount: Double, isCash: Bool = false) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "USD"
        formatter.currencySymbol = "$"
        
        // Convert to millions for display
        let millions = amount / 1_000_000
        
        if millions >= 1000 {
            // For billions, always show 1 decimal place
            let billions = millions / 1000
            formatter.minimumFractionDigits = 1
            formatter.maximumFractionDigits = 1
            
            if let formattedNumber = formatter.string(from: NSNumber(value: billions)) {
                return formattedNumber + " Billion"
            }
        } else if millions < 1 {
            formatter.usesGroupingSeparator = true
            formatter.minimumFractionDigits = 0
            formatter.maximumFractionDigits = 0
            
            if let formattedNumber = formatter.string(from: NSNumber(value: amount)) {
                return formattedNumber
            }
        } else {
            // For millions
            if isCash {
                if millions.truncatingRemainder(dividingBy: 1) == 0 {
                    // Whole number, no decimal
                    formatter.minimumFractionDigits = 0
                    formatter.maximumFractionDigits = 0
                } else {
                    // Has decimal, show 1 decimal place
                    formatter.minimumFractionDigits = 1
                    formatter.maximumFractionDigits = 1
                }
            } else {
                formatter.minimumFractionDigits = 0
                formatter.maximumFractionDigits = 0
            }
            
            if let formattedNumber = formatter.string(from: NSNumber(value: millions)) {
                return formattedNumber + " Million"
            }
        }
        
        // Fallback if formatter fails
        return String(format: "$%.0f Million", millions)
    }
    
    // MARK: - Next Draw Text Formatting
    
    static func formatNextDrawText(from now: Date, to nextDrawingDate: Date) -> String {
        let calendar = Calendar.current
        let components = calendar.dateComponents([.day, .hour, .minute], from: now, to: nextDrawingDate)
        
        if let days = components.day, let hours = components.hour, let minutes = components.minute {
            let totalHours = days * 24 + hours
            let totalMinutes = totalHours * 60 + minutes
            
            if totalMinutes < 0 {
                // Drawing has already happened - show time elapsed
                let elapsedMinutes = abs(totalMinutes)
                let elapsedHours = abs(totalHours)
                let elapsedDays = abs(days)
                
                if elapsedMinutes < 60 {
                    return "Drawed \(elapsedMinutes) minute\(elapsedMinutes > 1 ? "s" : "") ago"
                } else if elapsedHours < 24 {
                    return "Drawed \(elapsedHours) hour\(elapsedHours > 1 ? "s" : "") ago"
                } else if elapsedDays == 1 {
                    return "Drawed yesterday"
                } else {
                    return "Drawed \(elapsedDays) days ago"
                }
            } else if totalMinutes < 60 {
                if totalMinutes == 0 {
                    return "Soon"
                } else {
                    return "In \(totalMinutes) minute\(minutes > 1 ? "s" : "")"
                }
            } else if calendar.isDateInTomorrow(nextDrawingDate) {
                return "Tomorrow, \(hourRepresentation(nextDrawingDate))"
            } else if totalHours < 24 {
                return "In \(totalHours) hour\(totalHours > 1 ? "s" : "")"
            } else if days < 2 {
                return formatDateToDayMonth(nextDrawingDate)
            } else {
                return "In \(days) days"
            }
        } else {
            return "Today"
        }
    }
    
    // MARK: - Date String Representation
    
    static func formatDateToDayMonth(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "EEEE, MMM d"
        return formatter.string(from: date)
    }

    static func formatWidgetNextDrawingDate(_ date: Date) -> String {
        let calendar = Calendar.current
        let now = Date()
        
        // Check if the drawing date is today
        if calendar.isDate(date, inSameDayAs: now) {
            return "Today, \(hourRepresentation(date))"
        } else {
            // If it's not today, show full date format
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "EEEE, MMM d" // Friday, Aug 2
            return dateFormatter.string(from: date)
        }
    }
    
    static func hourRepresentation(_ date: Date) -> String {
        let calendar = Calendar.current
        let formatter = DateFormatter()
        formatter.dateFormat = "ha"
        
        // Round up to next hour if there are any minutes
        let timeComponents = calendar.dateComponents([.hour, .minute], from: date)
        var roundedDate = date
        
        if let hour = timeComponents.hour, let minute = timeComponents.minute {
            if minute > 50 {
                // Round up to next hour
                let nextHour = (hour + 1) % 24
                var roundedComponents = DateComponents()
                roundedComponents.year = calendar.component(.year, from: date)
                roundedComponents.month = calendar.component(.month, from: date)
                roundedComponents.day = calendar.component(.day, from: date)
                roundedComponents.hour = nextHour
                roundedComponents.minute = 0
                roundedDate = calendar.date(from: roundedComponents) ?? date
            } else if minute > 0 {
                formatter.dateFormat = "h:mm a"
            }
        }
        
        return formatter.string(from: roundedDate)
    }
}
