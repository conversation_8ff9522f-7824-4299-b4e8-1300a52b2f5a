//
//  ErrorCardView.swift
//  LotteryLightBoxAugment
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/8/25.
//

import SwiftUI

struct ErrorCardView: View {
    let lotteryType: LotteryType
    
    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Circle()
                    .fill(lotteryType == .powerball ? .red : .yellow)
                    .frame(width: 12, height: 12)
                
                LEDLabelView(lotteryType.rawValue, color: lotteryType == .powerball ? .red : .yellow, size: 14)
                
                Spacer()
            }
            
            LEDTextView("ERROR", color: .red, size: 20)
            LEDLabelView("NO DATA", color: .red, size: 16)
            LEDLabelView("Check connection", color: .gray, size: 12)
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.black)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color.red.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

#Preview {
    VStack(spacing: 20) {
        ErrorCardView(lotteryType: .powerball)
        ErrorCardView(lotteryType: .megaMillions)
    }
    .padding()
    .background(Color.black)
} 
