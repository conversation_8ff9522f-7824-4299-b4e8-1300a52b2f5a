//
//  WinnersView.swift
//  LotteryLightBoxAugment
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/8/25.
//

import SwiftUI

struct WinnersView: View {
    let winners: [Winner]
    let lotteryColor: Color
    
    private var groupedWinners: [(amount: String, states: String, hasJackpot: Bool)] {
        // Group winners by amount
        let grouped = Dictionary(grouping: winners) { $0.amount }
        
        // Convert to sorted array with formatted states
        return grouped.map { (amount, winners) in
            // Count occurrences of each state
            let stateCounts = Dictionary(grouping: winners) { $0.label }
                .mapValues { $0.count }
            
            // Format states with counts
            let formattedStates = stateCounts.map { state, count in
                count > 1 ? "\(state)(\(count))" : state
            }.sorted().joined(separator: ", ")
            
            // Check if this group has any jackpot winners
            let hasJackpot = winners.contains { $0.type == .jackpot }
            
            return (amount: amount, states: formattedStates, hasJackpot: hasJackpot)
        }.sorted { lhs, rhs in
            // Sort by amount (descending) - put higher amounts first
            // Extract numeric value for comparison
            let lhsValue = extractNumericValue(from: lhs.amount)
            let rhsValue = extractNumericValue(from: rhs.amount)
            return lhsValue > rhsValue
        }
    }
    
    private func extractNumericValue(from amount: String) -> Double {
        let cleanAmount = amount.replacingOccurrences(of: "$", with: "")
                               .replacingOccurrences(of: ",", with: "")
        
        if cleanAmount.contains("Billion") {
            let value = Double(cleanAmount.replacingOccurrences(of: " Billion", with: "")) ?? 0
            return value * 1_000_000_000
        } else if cleanAmount.contains("Million") {
            let value = Double(cleanAmount.replacingOccurrences(of: " Million", with: "")) ?? 0
            return value * 1_000_000
        } else {
            return Double(cleanAmount) ?? 0
        }
    }
    
    var body: some View {
        VStack(spacing: 4) {
            ForEach(groupedWinners, id: \.amount) { group in
                VStack(spacing: 2) {
                    HStack(alignment: .top) {
                        LEDLabelView(group.states, color: .green, size: 12)
                            .lineLimit(nil)
                            .multilineTextAlignment(.leading)
                        Spacer()
                        HStack(spacing: 6) {
                            if group.hasJackpot {
                                Circle()
                                    .fill(lotteryColor)
                                    .frame(width: 8, height: 8)
                            }
                            LEDLabelView(group.amount, color: .green, size: 12)
                        }
                    }
                }
            }
        }
        .padding(.top, 4)
    }
}

#Preview {
    let sampleWinners = [
        Winner(label: "TX", amount: "$2 Million", type: .match5),
        Winner(label: "CA", amount: "$1 Million", type: .match5),
        Winner(label: "CA", amount: "$1 Million", type: .match5),
        Winner(label: "FL", amount: "$1 Million", type: .match5),
        Winner(label: "IL", amount: "$1 Million", type: .match5),
        Winner(label: "IL", amount: "$1 Million", type: .match5),
        Winner(label: "MA", amount: "$1 Million", type: .match5),
        Winner(label: "MA", amount: "$1 Million", type: .match5),
        Winner(label: "NE", amount: "$1 Million", type: .match5),
        Winner(label: "AL", amount: "$1 Million", type: .match5),
        Winner(label: "AK", amount: "$1 Million", type: .match5),
        Winner(label: "NY", amount: "$1 Million", type: .match5),
        Winner(label: "WA", amount: "$100 Million", type: .jackpot)
    ]
    
    VStack(spacing: 20) {
        WinnersView(winners: sampleWinners, lotteryColor: .blue)
    }
    .padding()
    .background(Color.black)
} 
