//
//  LotteryCardView.swift
//  LotteryLightBoxAugment
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/8/25.
//

import SwiftUI

struct LotteryBallView: View {
    let number: String
    let backgroundColor: Color
    let textColor: Color
    
    var body: some View {
        Text(number)
            .font(.system(size: 16, weight: .bold, design: .monospaced))
            .foregroundColor(textColor)
            .frame(width: 32, height: 32)
            .background(
                Circle()
                    .fill(backgroundColor)
            )
    }
}

struct LotteryCardView: View {
    let drawingInfo: DrawingInfo
    
    init(_ drawingInfo: DrawingInfo) {
        self.drawingInfo = drawingInfo
    }
    
    private var lotteryColor: Color {
        switch drawingInfo.lotteryType {
        case .powerball:
            return .red
        case .megaMillions:
            return .yellow
        case .superLottoPlus:
            return .blue
        }
    }
    
    private func nextDrawText(currentDate: Date) -> String {
        let nextDrawText = DataFormatter.formatNextDrawText(
            from: currentDate,
            to: drawingInfo.nextDrawingDate
        )
        
        if nextDrawText.starts(with: "Drawed") {
            return nextDrawText
        } else {
            return "Next Draw: \(nextDrawText)"
        }
    }
    
    private func lastDrawText(_ date: Date) -> String {
        let lastDrawText = DataFormatter.formatDateToDayMonth(drawingInfo.drawingDate)
        
        return "Last Draw: \(lastDrawText)"
    }
    
    var body: some View {
        VStack(spacing: 8) {
            // Lottery header
            HStack {
                TimelineView(.periodic(from: Date(), by: 60.0)) { context in
                    if context.date > drawingInfo.nextDrawingDate {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: lotteryColor))
                    } else {
                        Circle()
                            .fill(lotteryColor)
                            .frame(width: 12, height: 12)
                    }
                }
                
                LEDLabelView(drawingInfo.lotteryType.rawValue, color: lotteryColor, size: 14)
                
                Spacer()
            }
            
            // Jackpot amount
            LEDNumberView(drawingInfo.jackpot, color: .red, size: 28)
                .padding(10)
            
            // Cash value or Result Pending
            TimelineView(.periodic(from: Date(), by: 60.0)) { context in
                if context.date > drawingInfo.nextDrawingDate {
                    LEDLabelView("Result Pending", color: .orange, size: 16)
                } else {
                    LEDLabelView("Cash: \(drawingInfo.cash)", color: .green, size: 16)
                }
            }
            
            // Next drawing - with real-time updates
            TimelineView(.periodic(from: Date(), by: 60.0)) { context in
                LEDLabelView(nextDrawText(currentDate: context.date), color: .gray, size: 14)
            }
            
            Divider()
                .frame(height: 1)
                .overlay(lotteryColor.opacity(0.3))
                .padding([.top], 20)
            
            VStack(spacing: 20) {
                LEDLabelView(lastDrawText(drawingInfo.drawingDate), color: .gray, size: 14)
                
                // Winning numbers
                VStack(spacing: 4) {
                    HStack(spacing: 12) {
                        ForEach(drawingInfo.winningNumbers, id: \.self) { number in
                            LotteryBallView(
                                number: "\(number)",
                                backgroundColor: .white,
                                textColor: .black
                            )
                        }
                        
                        // The ball (Powerball/Mega Ball)
                        LotteryBallView(
                            number: "\(drawingInfo.theBall)",
                            backgroundColor: lotteryColor,
                            textColor: .black
                        )
                    }
                }
                
                if !drawingInfo.winners.isEmpty {
                    // Winners section
                    WinnersView(winners: drawingInfo.winners, lotteryColor: lotteryColor)
                }
            }.padding([.top], 20)
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.black)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(lotteryColor.opacity(0.3), lineWidth: 3)
                )
        )
    }
}



#Preview {
    let powerball = DrawingInfo(
        jackpot: "$1.2 Billion",
        cash: "$565.6 Million",
        drawingDate: Date(),
        nextDrawingDate: Calendar.current.date(byAdding: .day, value: 1, to: Date())!,
        winningNumbers: [1, 2, 3, 4, 5],
        theBall: 6,
        winners: [
            .init(label: "CA", amount: "$1 Million", type: .match5),
            .init(label: "TX", amount: "$10 Million", type: .jackpot),
        ],
        lotteryType: .powerball
    )
    
    let megaMillions = DrawingInfo(
        jackpot: "$500 Million",
        cash: "$265.6 Million",
        drawingDate: Date(),
        nextDrawingDate: Calendar.current.date(byAdding: .day, value: 2, to: Date())!,
        winningNumbers: [1, 2, 3, 4, 5],
        theBall: 6,
        winners: [
            .init(label: "CA", amount: "$1 Million", type: .match5),
            .init(label: "CA", amount: "$1 Million", type: .match5),
            .init(label: "TX", amount: "$2 Million", type: .match5),
        ],
        lotteryType: .megaMillions,
    )
    
    let superLottoPlus = DrawingInfo(
        jackpot: "$27 Million",
        cash: "$12.0 Million",
        drawingDate: Date(),
        nextDrawingDate: Calendar.current.date(byAdding: .second, value: -60, to: Date())!,
        winningNumbers: [1, 2, 3, 4, 5],
        theBall: 6,
        winners: [
            .init(label: "Match 5 + Mega", amount: "$26 Million", type: .jackpot),
            .init(label: "Match 5", amount: "$33,651", type: .match5),
            .init(label: "Match 5", amount: "$33,651", type: .match5),
        ],
        lotteryType: .superLottoPlus,
    )
    
    VStack(spacing: 20) {
        LotteryCardView(powerball)
//        LotteryCardView(megaMillions)
        LotteryCardView(superLottoPlus)
    }
    .padding()
    .background(Color.black)
} 
