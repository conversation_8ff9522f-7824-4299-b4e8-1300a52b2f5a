//
//  LEDTextView.swift
//  LotteryLightBoxAugment
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/8/25.
//

import SwiftUI

struct LEDTextView: View {
    let text: String
    let color: Color
    let size: CGFloat
    
    init(_ text: String, color: Color = .red, size: CGFloat = 24) {
        self.text = text
        self.color = color
        self.size = size
    }
    
    var body: some View {
        Text(text)
            .font(.system(size: size, weight: .bold, design: .monospaced))
            .foregroundColor(color)
    }
}

struct LEDNumberView: View {
    let number: String
    let color: Color
    let size: CGFloat
    
    init(_ number: String, color: Color = .red, size: CGFloat = 24) {
        self.number = number
        self.color = color
        self.size = size
    }
    
    var body: some View {
        LEDTextView(number, color: color, size: size)
    }
}

struct LEDLabelView: View {
    let text: String
    let color: Color
    let size: CGFloat
    
    init(_ text: String, color: Color = .blue, size: CGFloat = 16) {
        self.text = text
        self.color = color
        self.size = size
    }
    
    var body: some View {
        LEDTextView(text, color: color, size: size)
    }
}

#Preview {
    VStack(spacing: 20) {
        LEDNumberView("$1.2B", color: .red, size: 32)
        LEDLabelView("NEXT DRAW", color: .blue, size: 18)
        LEDLabelView("Tomorrow", color: .yellow, size: 16)
    }
    .padding()
    .background(Color.black)
} 
