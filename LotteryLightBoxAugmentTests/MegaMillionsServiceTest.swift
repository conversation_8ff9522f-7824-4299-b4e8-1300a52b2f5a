//
//  MegaMillionsServiceTest.swift
//  LotteryLightboxTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/1/25.
//

import Foundation
import Testing
@testable import LotteryLightBoxAugment

class MegaMillionsServiceTest {
    @Test func testMegaMillionsServiceWithMockData() async throws {
        // Load JSON from file
        let testBundle = Bundle(for: type(of: self))
        guard let jsonPath = testBundle.path(forResource: "megamillions", ofType: "json"),
              let mockJSON = try? String(contentsOfFile: jsonPath, encoding: .utf8) else {
            throw NSError(domain: "TestError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Could not load megamillions.json file"])
        }

        let service = MegaMillionsService(response: mockJSON)
        let drawingInfo = try await service.fetchNextDrawingInfo()

        // Verify the parsed data matches expected values from the JSON
        // NextPrizePool: 326000000.0 -> "$326 Million"
        // NextCashValue: 145700000.0 -> "$145.7 Million"
        #expect(drawingInfo.jackpot == "$326 Million")
        #expect(drawingInfo.cash == "$145.7 Million")

        // Verify winning numbers (N1: 26, N2: 49, N3: 58, N4: 61, N5: 63)
        #expect(drawingInfo.winningNumbers == [26, 49, 58, 61, 63])

        // Verify Mega Ball (MBall: 9)
        #expect(drawingInfo.theBall == 9)

        // Verify winners information - should have 3 Match 5 winners
        #expect(drawingInfo.winners.count == 3)

        let sortedWinners = drawingInfo.winners.sorted { $0.label < $1.label }

        // GA should get $2 Million (2x multiplier)
        #expect(sortedWinners[0].label == "GA")
        #expect(sortedWinners[0].amount == "$2 Million")
        #expect(sortedWinners[0].type == .match5)

        // NJ should get $3 Million (3x multiplier)
        #expect(sortedWinners[1].label == "NJ")
        #expect(sortedWinners[1].amount == "$3 Million")
        #expect(sortedWinners[1].type == .match5)

        // WA should get $4 Million (4x multiplier)
        #expect(sortedWinners[2].label == "WA")
        #expect(sortedWinners[2].amount == "$4 Million")
        #expect(sortedWinners[2].type == .match5)
    }

    @Test func testMegaMillionsServiceErrorHandling() async throws {
        let invalidJSON = "{\"invalid\": \"json structure\"}"

        let service = MegaMillionsService(response: invalidJSON)

        await #expect(throws: LotteryServiceError.parsingError) {
            try await service.fetchNextDrawingInfo()
        }
    }

    @Test func testMegaMillionsServiceWithMalformedInnerJSON() async throws {
        let malformedJSON = "{\"d\": \"not valid json\"}"

        let service = MegaMillionsService(response: malformedJSON)

        await #expect(throws: LotteryServiceError.parsingError) {
            try await service.fetchNextDrawingInfo()
        }
    }

    @Test func testMegaMillionsServiceWithMissingFields() async throws {
        let incompleteJSON = "{\"d\": \"{\\\"Drawing\\\": {\\\"PlayDate\\\": \\\"2025-06-27T00:00:00\\\"}}\"}"

        let service = MegaMillionsService(response: incompleteJSON)

        await #expect(throws: LotteryServiceError.parsingError) {
            try await service.fetchNextDrawingInfo()
        }
    }

    @Test func testMegaMillionsServiceWithMatch5Winner() async throws {
        // Test data with Match 5 + 0 winner format
        let match5JSON = """
        {"d":"{\\"Drawing\\":{\\"PlayDate\\":\\"2025-07-04T00:00:00\\",\\"N1\\":10,\\"N2\\":20,\\"N3\\":30,\\"N4\\":40,\\"N5\\":50,\\"MBall\\":5,\\"Megaplier\\":-1,\\"UpdatedBy\\":\\"SERVICE\\",\\"UpdatedTime\\":\\"2025-07-04T23:05:01\\",\\"PlayDateTicks\\":638872704000000000,\\"IgnoreServiceUntil\\":null},\\"Jackpot\\":{\\"PlayDate\\":\\"2025-07-04T00:00:00\\",\\"CurrentPrizePool\\":100000000.0,\\"NextPrizePool\\":75000000.0,\\"CurrentCashValue\\":45000000.0,\\"NextCashValue\\":35000000.0,\\"Winners\\":0,\\"Verified\\":true,\\"UpdatedBy\\":\\"SERVICE\\",\\"UpdatedTime\\":\\"2025-07-05T01:01:02\\"},\\"MatchWinners\\":{\\"PlayDate\\":\\"2025-07-04T00:00:00\\",\\"WinnerText\\":\\"Match 5 + 0: <span class='bwState'>GA </span>\\",\\"RawText\\":\\"Match 5 + 0: GA \\",\\"ManualEntry\\":true},\\"NextDrawingDate\\":\\"2025-07-08T23:00:00\\"}"}
        """

        let service = MegaMillionsService(response: match5JSON)
        let drawingInfo = try await service.fetchNextDrawingInfo()

        // Verify the basic lottery information
        #expect(drawingInfo.jackpot == "$75 Million")
        #expect(drawingInfo.cash == "$35 Million")
        #expect(drawingInfo.winningNumbers == [10, 20, 30, 40, 50])
        #expect(drawingInfo.theBall == 5)

        // Verify Match 5 winner information - should be empty since no PrizeTiers/PrizeMatrix data
        #expect(drawingInfo.winners.count == 0)
    }

    @Test func testMegaMillionsServiceWithMultiplierWinners() async throws {
        // Use the JSON data directly instead of loading from file
        let mockJSON = """
        {
            "d": "{\\"Drawing\\":{\\"PlayDate\\":\\"2025-06-20T00:00:00\\",\\"N1\\":26,\\"N2\\":49,\\"N3\\":58,\\"N4\\":61,\\"N5\\":63,\\"MBall\\":9,\\"Megaplier\\":-1,\\"UpdatedBy\\":\\"SERVICE\\",\\"UpdatedTime\\":\\"2025-06-20T23:08:01\\",\\"PlayDateTicks\\":638859744000000000,\\"IgnoreServiceUntil\\":null},\\"Jackpot\\":{\\"PlayDate\\":\\"2025-06-20T00:00:00\\",\\"CurrentPrizePool\\":302000000.0,\\"NextPrizePool\\":326000000.0,\\"CurrentCashValue\\":136000000.0,\\"NextCashValue\\":145700000.0,\\"Winners\\":0,\\"Verified\\":true,\\"UpdatedBy\\":\\"SERVICE\\",\\"UpdatedTime\\":\\"2025-06-21T00:09:01\\"},\\"PrizeTiers\\":[{\\"PlayDate\\":\\"2025-06-20T00:00:00\\",\\"Tier\\":1,\\"IsMegaplier\\":false,\\"Winners\\":1,\\"Multiplier\\":\\"4x\\"},{\\"PlayDate\\":\\"2025-06-20T00:00:00\\",\\"Tier\\":1,\\"IsMegaplier\\":false,\\"Winners\\":1,\\"Multiplier\\":\\"2x\\"},{\\"PlayDate\\":\\"2025-06-20T00:00:00\\",\\"Tier\\":1,\\"IsMegaplier\\":false,\\"Winners\\":1,\\"Multiplier\\":\\"3x\\"}],\\"MatchWinners\\":{\\"PlayDate\\":\\"2025-06-20T00:00:00\\",\\"WinnerText\\":\\"Match 5 + 0: <span class='bwState'>GA,</span><span class='bwState'>NJ,</span><span class='bwState'>WA </span>\\",\\"RawText\\":\\"Match 5 + 0: GA, NJ, WA \\",\\"ManualEntry\\":true},\\"PrizeMatrix\\":{\\"PrizeTiers\\":[{\\"PrizeTier\\":1,\\"Mega2\\":2000000.0,\\"Mega3\\":3000000.0,\\"Mega4\\":4000000.0,\\"Mega5\\":5000000.0,\\"Mega10\\":10000000.0}]}}"
        }
        """

        let service = MegaMillionsService(response: mockJSON)
        let drawingInfo = try await service.fetchNextDrawingInfo()

        // Verify the basic lottery information
        #expect(drawingInfo.jackpot == "$326 Million")
        #expect(drawingInfo.cash == "$145.7 Million")
        #expect(drawingInfo.winningNumbers == [26, 49, 58, 61, 63])
        #expect(drawingInfo.theBall == 9)

        // Verify Match 5 winners with multipliers
        // From the JSON: GA, NJ, WA with 2x, 3x, 4x multipliers
        // Expected: GA ($2 Million), NJ ($3 Million), WA ($4 Million)
        #expect(drawingInfo.winners.count == 3)

        let sortedWinners = drawingInfo.winners.sorted { $0.label < $1.label }

        // GA should get $2 Million (2x multiplier)
        #expect(sortedWinners[0].label == "GA")
        #expect(sortedWinners[0].amount == "$2 Million")
        #expect(sortedWinners[0].type == .match5)

        // NJ should get $3 Million (3x multiplier)
        #expect(sortedWinners[1].label == "NJ")
        #expect(sortedWinners[1].amount == "$3 Million")
        #expect(sortedWinners[1].type == .match5)

        // WA should get $4 Million (4x multiplier)
        #expect(sortedWinners[2].label == "WA")
        #expect(sortedWinners[2].amount == "$4 Million")
        #expect(sortedWinners[2].type == .match5)
    }
}
