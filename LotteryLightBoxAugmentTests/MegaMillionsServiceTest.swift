//
//  MegaMillionsServiceTest.swift
//  LotteryLightboxTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/1/25.
//

import Foundation
import Testing
@testable import LotteryLightBoxAugment

class MegaMillionsServiceTest {
    @Test func testMegaMillionsServiceWithMockData() async throws {
        // Load JSON from file
        let testBundle = Bundle(for: type(of: self))
        guard let jsonPath = testBundle.path(forResource: "megamillions", ofType: "json"),
              let mockJSON = try? String(contentsOfFile: jsonPath, encoding: .utf8) else {
            throw NSError(domain: "TestError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Could not load megamillions.json file"])
        }

        let service = MegaMillionsService(response: mockJSON)
        let drawingInfo = try await service.fetchNextDrawingInfo()

        // Verify the parsed data matches expected values from the JSON
        // NextPrizePool: 50000000.0 -> "$50 Million"
        // NextCashValue: 22400000.0 -> "$22.4 Million"
        #expect(drawingInfo.jackpot == "$50 Million")
        #expect(drawingInfo.cash == "$22.4 Million")
        
        let formatter = ISO8601DateFormatter()
        // Verify drawing date (UpdatedTime: "2025-06-27T23:05:01")
        #expect(formatter.string(from: drawingInfo.drawingDate) == "2025-06-28T03:05:01Z")
        
        // Verify next drawing date (NextDrawingDate: "2025-07-01T23:00:00")
        #expect(formatter.string(from: drawingInfo.nextDrawingDate) == "2025-07-02T03:00:00Z")
        
        // Verify winning numbers (N1: 18, N2: 21, N3: 29, N4: 42, N5: 50)
        #expect(drawingInfo.winningNumbers == [18, 21, 29, 42, 50])
        
        // Verify Mega Ball (MBall: 2)
        #expect(drawingInfo.theBall == 2)
        
        // Verify winners information
        #expect(drawingInfo.winners.count > 0)
        let jackpotWinner = drawingInfo.winners.first { $0.type == .jackpot }
        #expect(jackpotWinner?.label == "VA")
        #expect(jackpotWinner?.type == .jackpot)
    }

    @Test func testMegaMillionsServiceErrorHandling() async throws {
        let invalidJSON = "{\"invalid\": \"json structure\"}"

        let service = MegaMillionsService(response: invalidJSON)

        await #expect(throws: LotteryServiceError.parsingError) {
            try await service.fetchNextDrawingInfo()
        }
    }

    @Test func testMegaMillionsServiceWithMalformedInnerJSON() async throws {
        let malformedJSON = "{\"d\": \"not valid json\"}"

        let service = MegaMillionsService(response: malformedJSON)

        await #expect(throws: LotteryServiceError.parsingError) {
            try await service.fetchNextDrawingInfo()
        }
    }

    @Test func testMegaMillionsServiceWithMissingFields() async throws {
        let incompleteJSON = "{\"d\": \"{\\\"Drawing\\\": {\\\"PlayDate\\\": \\\"2025-06-27T00:00:00\\\"}}\"}"

        let service = MegaMillionsService(response: incompleteJSON)

        await #expect(throws: LotteryServiceError.parsingError) {
            try await service.fetchNextDrawingInfo()
        }
    }

    @Test func testMegaMillionsServiceWithMatch5Winner() async throws {
        // Test data with Match 5 + 0 winner format
        let match5JSON = """
        {"d":"{\\"Drawing\\":{\\"PlayDate\\":\\"2025-07-04T00:00:00\\",\\"N1\\":10,\\"N2\\":20,\\"N3\\":30,\\"N4\\":40,\\"N5\\":50,\\"MBall\\":5,\\"Megaplier\\":-1,\\"UpdatedBy\\":\\"SERVICE\\",\\"UpdatedTime\\":\\"2025-07-04T23:05:01\\",\\"PlayDateTicks\\":638872704000000000,\\"IgnoreServiceUntil\\":null},\\"Jackpot\\":{\\"PlayDate\\":\\"2025-07-04T00:00:00\\",\\"CurrentPrizePool\\":100000000.0,\\"NextPrizePool\\":75000000.0,\\"CurrentCashValue\\":45000000.0,\\"NextCashValue\\":35000000.0,\\"Winners\\":0,\\"Verified\\":true,\\"UpdatedBy\\":\\"SERVICE\\",\\"UpdatedTime\\":\\"2025-07-05T01:01:02\\"},\\"MatchWinners\\":{\\"PlayDate\\":\\"2025-07-04T00:00:00\\",\\"WinnerText\\":\\"Match 5 + 0: <span class='bwState'>GA </span>\\",\\"RawText\\":\\"Match 5 + 0: GA \\",\\"ManualEntry\\":true},\\"NextDrawingDate\\":\\"2025-07-08T23:00:00\\"}"}
        """
        
        let service = MegaMillionsService(response: match5JSON)
        let drawingInfo = try await service.fetchNextDrawingInfo()
        
        // Verify the basic lottery information
        #expect(drawingInfo.jackpot == "$75 Million")
        #expect(drawingInfo.cash == "$35 Million")
        #expect(drawingInfo.winningNumbers == [10, 20, 30, 40, 50])
        #expect(drawingInfo.theBall == 5)
        
        // Verify Match 5 winner information
        #expect(drawingInfo.winners.count == 1)
        let match5Winner = drawingInfo.winners.first
        #expect(match5Winner?.label == "GA")
        #expect(match5Winner?.amount == "$1 Million")
        #expect(match5Winner?.type == .match5)
    }
}
