//
//  PowerballServiceTest.swift
//  LotteryLightboxTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/1/25.
//

import Foundation
import Testing
@testable import LotteryLightBoxAugment

class PowerballServiceTest {
    @Test func testPowerballServiceWithMockData() async throws {
        // Load HTML from file
        let testBundle = Bundle(for: type(of: self))
        guard let htmlPath = testBundle.path(forResource: "powerball", ofType: "html"),
              let mockHTML = try? String(contentsOfFile: htmlPath, encoding: .utf8) else {
            throw NSError(domain: "TestError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Could not load powerball.html file"])
        }

        let service = PowerballService(response: mockHTML)
        let drawingInfo = try await service.fetchNextDrawingInfo()

        // Debug: Print what we actually got
        print("=== PARSED RESULTS ===")
        print("Jackpot: \(drawingInfo.jackpot)")
        print("Cash: \(drawingInfo.cash)")
        print("Winning Numbers: \(drawingInfo.winningNumbers)")
        print("Powerball: \(drawingInfo.theBall)")
        print("Drawing Date: \(drawingInfo.drawingDate)")
        print("Winners: \(drawingInfo.winners)")
        print("=====================")

        // Test jackpot and cash parsing
        #expect(drawingInfo.jackpot == "$174 Million")
        #expect(drawingInfo.cash == "$79.7 Million")
        
        // Test winning numbers parsing
        #expect(drawingInfo.winningNumbers.count == 5)
        #expect(drawingInfo.winningNumbers == [13, 28, 44, 52, 55])
        
        // Test powerball number parsing
        #expect(drawingInfo.theBall == 6)
        
        // Test drawing date parsing
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let expectedDate = dateFormatter.date(from: "2025-06-30")!
        #expect(Calendar.current.isDate(drawingInfo.drawingDate, inSameDayAs: expectedDate))
        
        // Test winners parsing - all should be "None" according to the HTML
        #expect(drawingInfo.winners.isEmpty)
    }

    @Test func testPowerballServiceErrorHandling() async throws {
        let invalidHTML = "<html><body><p>No lottery data here</p></body></html>"

        let service = PowerballService(response: invalidHTML)
        
        await #expect(throws: LotteryServiceError.parsingError) {
            try await service.fetchNextDrawingInfo()
        }
    }
    
    /*
    @Test func testPowerballServiceWithWinners() async throws {
        // Create mock HTML with actual winners
        let mockHTML = """
        <html>
        <div class="card h-100 next-card next-powerball scheduled">
            <div class="row game-detail-group mb-3">
                <span class="game-title text-uppercase bg-dark text-yellow">Estimated Jackpot</span>
                <span class="game-jackpot-number text-xxxl lh-1 text-center">$50 Million</span>
            </div>
            <div class="row winners-group mb-3">
                <span class="game-title text-uppercase bg-dark text-yellow">Cash Value</span>
                <span class="game-jackpot-number text-lg lh-1 text-center">$25.5 Million</span>
            </div>
        </div>
        
        <div class="card h-100 number-card number-powerball complete">
            <h5 class="card-title mx-auto mb-3 lh-1 text-center title-date">Tue, Jul 1, 2025</h5>
            <div class="form-control col white-balls item-powerball">5</div>
            <div class="form-control col white-balls item-powerball">15</div>
            <div class="form-control col white-balls item-powerball">25</div>
            <div class="form-control col white-balls item-powerball">35</div>
            <div class="form-control col white-balls item-powerball">45</div>
            <div class="form-control col powerball item-powerball">10</div>
        </div>
        
        <div class="card h-100 winner-card winner-powerball complete">
            <div class="row winners-group mb-3">
                <span class="game-name lh-1 text-center">Powerball</span>
                <span class="winner-type lh-1 text-center">JACKPOT WINNERS</span>
                <span class="winner-location text-center text-red">TX</span>
            </div>
            <div class="row winners-group mb-3">
                <span class="game-name lh-1 text-center">Match 5 + Power Play</span>
                <span class="winner-type lh-1 text-center">$2 Million Winners</span>
                <span class="winner-location text-center text-red">NY (2), FL</span>
            </div>
            <div class="row winners-group">
                <span class="game-name lh-1 text-center">Match 5</span>
                <span class="winner-type lh-1 text-center">$1 Million Winners</span>
                <span class="winner-location text-center text-red">CA (3), IL</span>
            </div>
        </div>
        
        <div class="flip-clock d-flex col-auto flex-nowrap mx-auto count-down" data-drawdateutc="2025-07-05T02:59:00.0000000Z">
        </div>
        </html>
        """
        
        let service = PowerballService(response: mockHTML)
        let drawingInfo = try await service.fetchNextDrawingInfo()
        
        // Verify basic info
        #expect(drawingInfo.jackpot == "$50 Million")
        #expect(drawingInfo.cash == "$25.5 Million")
        #expect(drawingInfo.winningNumbers == [5, 15, 25, 35, 45])
        #expect(drawingInfo.theBall == 10)
        
        // Verify winners
        #expect(drawingInfo.winners.count == 7) // 1 jackpot + 3 match5 (2M) + 4 match5 (1M) = 8
        
        // Check jackpot winner
        let jackpotWinners = drawingInfo.winners.filter { $0.type == .jackpot }
        #expect(jackpotWinners.count == 1)
        #expect(jackpotWinners[0].state == "TX")
        #expect(jackpotWinners[0].amount == "Jackpot")
        
        // Check $2 Million winners (Match 5 + Power Play)
        let match5PowerWinners = drawingInfo.winners.filter { $0.amount == "$2 Million" }
        #expect(match5PowerWinners.count == 3) // NY (2) + FL (1)
        
        // Check $1 Million winners (Match 5)
        let match5Winners = drawingInfo.winners.filter { $0.amount == "$1 Million" }
        #expect(match5Winners.count == 4) // CA (3) + IL (1)
        
        // Verify state distribution
        let caWinners = drawingInfo.winners.filter { $0.state == "CA" }
        #expect(caWinners.count == 3)
        
        let nyWinners = drawingInfo.winners.filter { $0.state == "NY" }
        #expect(nyWinners.count == 2)
    }
    */
}
