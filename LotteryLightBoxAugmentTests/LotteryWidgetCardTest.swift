//
//  LotteryWidgetFormatterTest.swift
//  LotteryLightBoxAugmentTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/11/25.
//

import Foundation
import Testing
import SwiftUI
@testable import LotteryLightBoxAugment

class LotteryWidgetFormatterTest {
    
    // MARK: - NextDrawText Formatting Tests
    
    @Test func testNextDrawTextWithMinutes() async throws {
        let now = Date()
        let futureDate = now.addingTimeInterval(30 * 60) // 30 minutes
        
        let result = DataFormatter.formatNextDrawText(from: now, to: futureDate)
        #expect(result == "In 30 minutes")
    }
    
    @Test func testNextDrawTextWithHours() async throws {
        let now = Date()
        let futureDate = now.addingTimeInterval(5 * 3600) // 5 hours
        
        let result = DataFormatter.formatNextDrawText(from: now, to: futureDate)
        #expect(result == "In 5 hours")
    }
    
    @Test func testNextDrawTextWithTomorrow() async throws {
        let calendar = Calendar.current
        let now = Date()
        let tomorrow = calendar.date(byAdding: .day, value: 1, to: now)!
        let tomorrowAt11PM = calendar.date(bySettingHour: 23, minute: 0, second: 0, of: tomorrow)!
        
        let result = DataFormatter.formatNextDrawText(from: now, to: tomorrowAt11PM)
        #expect(result.hasPrefix("Tomorrow, "))
        #expect(result.hasSuffix("PM"))
    }
    
    @Test func testNextDrawTextWithMultipleDays() async throws {
        let now = Date()
        let futureDate = now.addingTimeInterval(3 * 24 * 3600) // 3 days
        
        let result = DataFormatter.formatNextDrawText(from: now, to: futureDate)
        #expect(result == "In 3 days")
    }
    
    @Test func testNextDrawTextWithOneMinute() async throws {
        let now = Date()
        let futureDate = now.addingTimeInterval(60) // 1 minute
        
        let result = DataFormatter.formatNextDrawText(from: now, to: futureDate)
        #expect(result == "In 1 minutes")
    }
    
    @Test func testNextDrawTextWithOneHour() async throws {
        let now = Date()
        let futureDate = now.addingTimeInterval(3600) // 1 hour
        
        let result = DataFormatter.formatNextDrawText(from: now, to: futureDate)
        #expect(result == "In 1 hour")
    }
    
    @Test func testNextDrawTextWithTwoHours() async throws {
        let now = Date()
        let futureDate = now.addingTimeInterval(2 * 3600) // 2 hours
        
        let result = DataFormatter.formatNextDrawText(from: now, to: futureDate)
        #expect(result == "In 2 hours")
    }
    
    @Test func testNextDrawTextWithTwentyThreeHours() async throws {
        let now = Date()
        let futureDate = now.addingTimeInterval(23 * 3600) // 23 hours
        
        let result = DataFormatter.formatNextDrawText(from: now, to: futureDate)
        #expect(result == "In 23 hours")
    }
    
    @Test func testNextDrawTextWithTwoDays() async throws {
        let now = Date()
        let futureDate = now.addingTimeInterval(2 * 24 * 3600) // 2 days
        
        let result = DataFormatter.formatNextDrawText(from: now, to: futureDate)
        #expect(result == "In 2 days")
    }
    
    @Test func testNextDrawTextWithExactlyOneDay() async throws {
        let now = Date()
        let futureDate = now.addingTimeInterval(24 * 3600) // Exactly 24 hours
        
        let result = DataFormatter.formatNextDrawText(from: now, to: futureDate)
        #expect(result.hasPrefix("Tomorrow, "))
    }
    
    @Test func testNextDrawTextWithAlmostTwoDays() async throws {
        let now = Date()
        let futureDate = now.addingTimeInterval(47 * 3600) // 47 hours (almost 2 days)
        
        let result = DataFormatter.formatNextDrawText(from: now, to: futureDate)
        #expect(result.hasPrefix("Tomorrow, "))
    }
    
    // MARK: - Edge Cases
    
    @Test func testNextDrawTextWithPastDate() async throws {
        let now = Date()
        let pastDate = now.addingTimeInterval(-3600) // 1 hour ago
        
        let result = DataFormatter.formatNextDrawText(from: now, to: pastDate)
        #expect(result == "Drawed 1 hour ago")
    }
    
    @Test func testNextDrawTextWithSameDate() async throws {
        let now = Date()
        
        let result = DataFormatter.formatNextDrawText(from: now, to: now)
        #expect(result == "In 0 minutes")
    }
} 
