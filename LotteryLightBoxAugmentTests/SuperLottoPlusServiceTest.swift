import Foundation
import Testing
@testable import LotteryLightBoxAugment

class SuperLottoPlusServiceTest {
    @Test func testSuperLottoPlusServiceWithMockData() async throws {
        // Load JSON from file
        let testBundle = Bundle(for: type(of: self))
        guard let jsonPath = testBundle.path(forResource: "superlottoplus", ofType: "json"),
              let mockJSON = try? String(contentsOfFile: jsonPath, encoding: .utf8) else {
            throw NSError(domain: "TestError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Could not load superlottoplus.json file"])
        }

        let service = SuperLottoPlusService(response: mockJSON)
        let drawingInfo = try await service.fetchNextDrawingInfo()

        // Debug: Print what we actually got
        print("=== PARSED RESULTS ===")
        print("Jackpot: \(drawingInfo.jackpot)")
        print("Cash: \(drawingInfo.cash)")
        print("Winning Numbers: \(drawingInfo.winningNumbers)")
        print("Mega: \(drawingInfo.theBall)")
        print("Drawing Date: \(drawingInfo.drawingDate)")
        print("Next Drawing Date: \(drawingInfo.nextDrawingDate)")
        print("Winners: \(drawingInfo.winners)")
        print("=====================")

        // Test jackpot and cash parsing (based on JSON data)
        #expect(drawingInfo.jackpot == "$27.0 Million")
        #expect(drawingInfo.cash == "$12.0 Million")
        
        // Test winning numbers parsing (from JSON: 4, 18, 19, 33, 43)
        #expect(drawingInfo.winningNumbers.count == 5)
        #expect(drawingInfo.winningNumbers == [4, 18, 19, 33, 43])
        
        // Test mega number parsing (from JSON: 20)
        #expect(drawingInfo.theBall == 20)
        
        // Test drawing date parsing (2025-07-19T07:00:00)
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let expectedDate = dateFormatter.date(from: "2025-07-19")!
        #expect(Calendar.current.isDate(drawingInfo.drawingDate, inSameDayAs: expectedDate))
        
        // Test next drawing date parsing (2025-07-23T07:00:00)
        let expectedNextDate = dateFormatter.date(from: "2025-07-23")!
        #expect(Calendar.current.isDate(drawingInfo.nextDrawingDate, inSameDayAs: expectedNextDate))
        
        // Test winners parsing
        #expect(drawingInfo.winners.isEmpty)
        
        // Test lottery type
        #expect(drawingInfo.lotteryType == .superLottoPlus)
    }

    @Test func testSuperLottoPlusServiceErrorHandling() async throws {
        let invalidJSON = """
        {
            "invalid": "json",
            "structure": "here"
        }
        """

        let service = SuperLottoPlusService(response: invalidJSON)
        
        await #expect(throws: LotteryServiceError.parsingError) {
            try await service.fetchNextDrawingInfo()
        }
    }
    
    @Test func testSuperLottoPlusServiceWithEmptyData() async throws {
        let emptyJSON = "{}"

        let service = SuperLottoPlusService(response: emptyJSON)
        
        await #expect(throws: LotteryServiceError.parsingError) {
            try await service.fetchNextDrawingInfo()
        }
    }
    
    @Test func testSuperLottoPlusServiceWithMalformedJSON() async throws {
        let malformedJSON = "{ invalid json structure"

        let service = SuperLottoPlusService(response: malformedJSON)
        
        await #expect(throws: LotteryServiceError.parsingError) {
            try await service.fetchNextDrawingInfo()
        }
    }
    
    @Test func testSuperLottoPlusServiceWithMissingNextDraw() async throws {
        let jsonWithoutNextDraw = """
        {
            "DrawGameId": 8,
            "Name": "SuperLotto Plus",
            "HasJackpot": true,
            "MostRecentDraw": {
                "DrawNumber": 3996,
                "DrawDate": "2025-07-19T07:00:00",
                "WinningNumbers": {
                    "0": {"Number": "4", "IsSpecial": false},
                    "1": {"Number": "18", "IsSpecial": false},
                    "2": {"Number": "19", "IsSpecial": false},
                    "3": {"Number": "33", "IsSpecial": false},
                    "4": {"Number": "43", "IsSpecial": false},
                    "5": {"Number": "20", "IsSpecial": true}
                }
            }
        }
        """

        let service = SuperLottoPlusService(response: jsonWithoutNextDraw)
        let drawingInfo = try await service.fetchNextDrawingInfo()

        // Should handle missing NextDraw gracefully
        #expect(drawingInfo.jackpot == "Unknown")
        #expect(drawingInfo.cash == "Unknown")
        #expect(drawingInfo.winningNumbers == [4, 18, 19, 33, 43])
        #expect(drawingInfo.theBall == 20)
    }
    
    @Test func testCurrencyFormattingBillions() async throws {
        let jsonWithBillions = """
        {
            "DrawGameId": 8,
            "Name": "SuperLotto Plus",
            "NextDraw": {
                "DrawNumber": 3997,
                "DrawDate": "2025-07-23T07:00:00",
                "JackpotAmount": 1500000000,
                "EstimatedCashValue": 750000000
            },
            "HasJackpot": true,
            "MostRecentDraw": {
                "DrawNumber": 3996,
                "DrawDate": "2025-07-19T07:00:00",
                "WinningNumbers": {
                    "0": {"Number": "1", "IsSpecial": false},
                    "1": {"Number": "2", "IsSpecial": false},
                    "2": {"Number": "3", "IsSpecial": false},
                    "3": {"Number": "4", "IsSpecial": false},
                    "4": {"Number": "5", "IsSpecial": false},
                    "5": {"Number": "6", "IsSpecial": true}
                }
            }
        }
        """

        let service = SuperLottoPlusService(response: jsonWithBillions)
        let drawingInfo = try await service.fetchNextDrawingInfo()

        #expect(drawingInfo.jackpot == "$1.5 Billion")
        #expect(drawingInfo.cash == "$750.0 Million")
    }
    
    @Test func testDrawingTimeConversion() async throws {
        let testBundle = Bundle(for: type(of: self))
        guard let jsonPath = testBundle.path(forResource: "superlottoplus", ofType: "json"),
              let mockJSON = try? String(contentsOfFile: jsonPath, encoding: .utf8) else {
            throw NSError(domain: "TestError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Could not load superlottoplus.json file"])
        }

        let service = SuperLottoPlusService(response: mockJSON)
        let drawingInfo = try await service.fetchNextDrawingInfo()

        // Test that drawing times are set to 7:57 PM Pacific Time
        let calendar = Calendar.current
        let drawingTimeComponents = calendar.dateComponents([.hour, .minute], from: drawingInfo.drawingDate)
        let nextDrawingTimeComponents = calendar.dateComponents([.hour, .minute], from: drawingInfo.nextDrawingDate)
        
        #expect(drawingTimeComponents.hour == 19) // 7 PM
        #expect(drawingTimeComponents.minute == 57) // 57 minutes
        #expect(nextDrawingTimeComponents.hour == 19) // 7 PM  
        #expect(nextDrawingTimeComponents.minute == 57) // 57 minutes
    }
    
    @Test func testWinnerExtraction() async throws {
        let jsonWithWinners = """
        {
            "DrawGameId": 8,
            "Name": "SuperLotto Plus",
            "NextDraw": {
                "DrawNumber": 3997,
                "DrawDate": "2025-07-23T07:00:00",
                "JackpotAmount": 27000000,
                "EstimatedCashValue": 12000000
            },
            "HasJackpot": true,
            "MostRecentDraw": {
                "DrawNumber": 3996,
                "DrawDate": "2025-07-19T07:00:00",
                "WinningNumbers": {
                    "0": {"Number": "1", "IsSpecial": false},
                    "1": {"Number": "2", "IsSpecial": false},
                    "2": {"Number": "3", "IsSpecial": false},
                    "3": {"Number": "4", "IsSpecial": false},
                    "4": {"Number": "5", "IsSpecial": false},
                    "5": {"Number": "6", "IsSpecial": true}
                },
                "Prizes": {
                    "1": {
                        "PrizeTypeDescription": "5 + Mega",
                        "Count": 2,
                        "Amount": 26000000
                    },
                    "2": {
                        "PrizeTypeDescription": "5",
                        "Count": 3,
                        "Amount": 33651
                    },
                    "3": {
                        "PrizeTypeDescription": "4 + Mega",
                        "Count": 9,
                        "Amount": 1869
                    }
                }
            }
        }
        """

        let service = SuperLottoPlusService(response: jsonWithWinners)
        let drawingInfo = try await service.fetchNextDrawingInfo()

        // Test winner extraction
        #expect(drawingInfo.winners.count == 5) // 2 jackpot + 3 match5
        
        // Test jackpot winners
        let jackpotWinners = drawingInfo.winners.filter { $0.type == .jackpot }
        #expect(jackpotWinners.count == 2)
        #expect(jackpotWinners.allSatisfy { $0.label == "5 + Mega" })
        #expect(jackpotWinners.allSatisfy { $0.amount == "$26.0 Million" })
        
        // Test match5 winners
        let match5Winners = drawingInfo.winners.filter { $0.type == .match5 }
        #expect(match5Winners.count == 3)
        #expect(match5Winners.allSatisfy { $0.label == "5" })
        #expect(match5Winners.allSatisfy { $0.amount == "$33651" })
    }
} 
