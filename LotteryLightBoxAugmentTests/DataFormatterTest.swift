//
//  DataFormatterTest.swift
//  LotteryLightBoxAugmentTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/11/25.
//

import Foundation
import Testing
import SwiftUI
@testable import LotteryLightBoxAugment

class DataFormatterTest {
    
    // MARK: - Next Draw Text Time Rounding Tests
    
    @Test func testNextDrawTextWithMinutesRoundsUpToNextHour() throws {
        // Test case: 7:59PM tomorrow should display as "Tomorrow, 8PM"
        let calendar = Calendar.current
        
        // Set current time to 12:00 PM today to ensure tomorrow's time is >24 hours away
        var nowComponents = calendar.dateComponents([.year, .month, .day], from: Date())
        nowComponents.hour = 12  // 12PM
        nowComponents.minute = 0
        let now = try #require(calendar.date(from: nowComponents))
        
        // Create a date for tomorrow at 7:59PM
        var tomorrowComponents = calendar.dateComponents([.year, .month, .day], from: now)
        tomorrowComponents.day! += 1
        tomorrowComponents.hour = 19  // 7PM
        tomorrowComponents.minute = 59
        
        let tomorrowDate = try #require(calendar.date(from: tomorrowComponents))
        let result = DataFormatter.formatNextDrawText(from: now, to: tomorrowDate)
        
        #expect(result == "Tomorrow, 8PM")
    }
    
    @Test func testNextDrawTextWithMinutesRoundsUpToNextHourAM() throws {
        // Test case: 11:30PM tomorrow should display as "Tomorrow, 12AM"
        let calendar = Calendar.current
        
        // Set current time to 12:00 PM today to ensure tomorrow's time is >24 hours away
        var nowComponents = calendar.dateComponents([.year, .month, .day], from: Date())
        nowComponents.hour = 12  // 12PM
        nowComponents.minute = 0
        let now = try #require(calendar.date(from: nowComponents))
        
        // Create a date for tomorrow at 11:30PM
        var tomorrowComponents = calendar.dateComponents([.year, .month, .day], from: now)
        tomorrowComponents.day! += 1
        tomorrowComponents.hour = 23  // 11PM
        tomorrowComponents.minute = 30
        
        let tomorrowDate = try #require(calendar.date(from: tomorrowComponents))
        let result = DataFormatter.formatNextDrawText(from: now, to: tomorrowDate)
        
        #expect(result == "Tomorrow, 12AM")
    }
    
    @Test func testNextDrawTextWithoutMinutesNoRounding() throws {
        // Test case: 7:00PM tomorrow should display as "Tomorrow, 7PM" (no rounding)
        let calendar = Calendar.current
        
        // Set current time to 12:00 PM today to ensure tomorrow's time is >24 hours away
        var nowComponents = calendar.dateComponents([.year, .month, .day], from: Date())
        nowComponents.hour = 12  // 12PM
        nowComponents.minute = 0
        let now = try #require(calendar.date(from: nowComponents))
        
        // Create a date for tomorrow at exactly 7:00PM
        var tomorrowComponents = calendar.dateComponents([.year, .month, .day], from: now)
        tomorrowComponents.day! += 1
        tomorrowComponents.hour = 19  // 7PM
        tomorrowComponents.minute = 0
        
        let tomorrowDate = try #require(calendar.date(from: tomorrowComponents))
        let result = DataFormatter.formatNextDrawText(from: now, to: tomorrowDate)
        
        #expect(result == "Tomorrow, 7PM")
    }
    
    @Test func testNextDrawTextWithSingleMinuteRoundsUp() throws {
        // Test case: 7:01PM tomorrow should display as "Tomorrow, 8PM"
        let calendar = Calendar.current
        
        // Set current time to 12:00 PM today to ensure tomorrow's time is >24 hours away
        var nowComponents = calendar.dateComponents([.year, .month, .day], from: Date())
        nowComponents.hour = 12  // 12PM
        nowComponents.minute = 0
        let now = try #require(calendar.date(from: nowComponents))
        
        // Create a date for tomorrow at 7:01PM
        var tomorrowComponents = calendar.dateComponents([.year, .month, .day], from: now)
        tomorrowComponents.day! += 1
        tomorrowComponents.hour = 19  // 7PM
        tomorrowComponents.minute = 1
        
        let tomorrowDate = try #require(calendar.date(from: tomorrowComponents))
        let result = DataFormatter.formatNextDrawText(from: now, to: tomorrowDate)
        
        #expect(result == "Tomorrow, 8PM")
    }
    
    @Test func testNextDrawTextMidnightTransition() throws {
        // Test case: 11:59PM tomorrow should display as "Tomorrow, 12AM"
        let calendar = Calendar.current
        
        // Set current time to 12:00 PM today to ensure tomorrow's time is >24 hours away
        var nowComponents = calendar.dateComponents([.year, .month, .day], from: Date())
        nowComponents.hour = 12  // 12PM
        nowComponents.minute = 0
        let now = try #require(calendar.date(from: nowComponents))
        
        // Create a date for tomorrow at 11:59PM
        var tomorrowComponents = calendar.dateComponents([.year, .month, .day], from: now)
        tomorrowComponents.day! += 1
        tomorrowComponents.hour = 23  // 11PM
        tomorrowComponents.minute = 59
        
        let tomorrowDate = try #require(calendar.date(from: tomorrowComponents))
        let result = DataFormatter.formatNextDrawText(from: now, to: tomorrowDate)
        
        #expect(result == "Tomorrow, 12AM")
    }
    
    @Test func testNextDrawTextNoonTransition() throws {
        // Test case: 11:45AM tomorrow should display as "Tomorrow, 12PM"
        let calendar = Calendar.current
        
        // Set current time to 10:00 AM today to ensure tomorrow's time is >24 hours away
        var nowComponents = calendar.dateComponents([.year, .month, .day], from: Date())
        nowComponents.hour = 10  // 10AM
        nowComponents.minute = 0
        let now = try #require(calendar.date(from: nowComponents))
        
        // Create a date for tomorrow at 11:45AM
        var tomorrowComponents = calendar.dateComponents([.year, .month, .day], from: now)
        tomorrowComponents.day! += 1
        tomorrowComponents.hour = 11  // 11AM
        tomorrowComponents.minute = 45
        
        let tomorrowDate = try #require(calendar.date(from: tomorrowComponents))
        let result = DataFormatter.formatNextDrawText(from: now, to: tomorrowDate)
        
        #expect(result == "Tomorrow, 12PM")
    }
    
    // MARK: - Regression Tests
    
    @Test func testNextDrawTextInHoursNoRounding() throws {
        // Test case: Ensure "In X hours" format is not affected by rounding logic
        let calendar = Calendar.current
        let now = Date()
        
        // Create a date for 5 hours from now
        let futureDate = calendar.date(byAdding: .hour, value: 5, to: now)!
        let result = DataFormatter.formatNextDrawText(from: now, to: futureDate)
        
        #expect(result == "In 5 hours")
    }
    
    @Test func testNextDrawTextInMinutesNoRounding() throws {
        // Test case: Ensure "In X minutes" format is not affected by rounding logic
        let calendar = Calendar.current
        let now = Date()
        
        // Create a date for 30 minutes from now
        let futureDate = calendar.date(byAdding: .minute, value: 30, to: now)!
        let result = DataFormatter.formatNextDrawText(from: now, to: futureDate)
        
        #expect(result == "In 30 minutes")
    }
    
    @Test func testNextDrawTextInDaysNoRounding() throws {
        // Test case: Ensure "In X days" format is not affected by rounding logic
        let calendar = Calendar.current
        let now = Date()
        
        // Create a date for 3 days from now
        let futureDate = calendar.date(byAdding: .day, value: 3, to: now)!
        let result = DataFormatter.formatNextDrawText(from: now, to: futureDate)
        
        #expect(result == "In 3 days")
    }
    
    // MARK: - Other DataFormatter Tests
    
    @Test func testFormatWidgetJackpot() {
        let result1 = DataFormatter.formatWidgetJackpot("$50 Million")
        #expect(result1 == "50")
        
        let result2 = DataFormatter.formatWidgetJackpot("$1.2 Billion")
        #expect(result2 == "1.2B")
        
        let result3 = DataFormatter.formatWidgetJackpot("$750 Million")
        #expect(result3 == "750")
    }
    
    @Test func testFormatWidgetCash() {
        let result1 = DataFormatter.formatWidgetCash("$25.5 Million")
        #expect(result1 == "$25.5M")
        
        let result2 = DataFormatter.formatWidgetCash("$1.1 Billion")
        #expect(result2 == "$1.1B")
        
        let result3 = DataFormatter.formatWidgetCash("$500,000")
        #expect(result3 == "$500,000")
    }
    
    // MARK: - Format Currency Tests
    
    @Test func testFormatCurrencyLessThanMillion() {
        // Test amounts less than 1 million should have comma separators
        let result1 = DataFormatter.formatCurrency(1500.0)
        #expect(result1 == "$1,500")
        
        let result2 = DataFormatter.formatCurrency(15000.0)
        #expect(result2 == "$15,000")
        
        let result3 = DataFormatter.formatCurrency(150000.0)
        #expect(result3 == "$150,000")
        
        let result4 = DataFormatter.formatCurrency(999999.0)
        #expect(result4 == "$999,999")
    }
    
    @Test func testFormatCurrencySmallAmounts() {
        // Test very small amounts
        let result1 = DataFormatter.formatCurrency(100.0)
        #expect(result1 == "$100")
        
        let result2 = DataFormatter.formatCurrency(50.0)
        #expect(result2 == "$50")
        
        let result3 = DataFormatter.formatCurrency(1.0)
        #expect(result3 == "$1")
    }
    
    @Test func testFormatCurrencyMillions() {
        // Test amounts in millions
        let result1 = DataFormatter.formatCurrency(1_000_000.0)
        #expect(result1 == "$1 Million")
        
        let result2 = DataFormatter.formatCurrency(25_500_000.0)
        #expect(result2 == "$26 Million")
        
        let result3 = DataFormatter.formatCurrency(750_000_000.0)
        #expect(result3 == "$750 Million")
    }
    
    @Test func testFormatCurrencyMillionsWithCash() {
        // Test cash mode for millions (should show decimals for non-whole numbers)
        let result1 = DataFormatter.formatCurrency(25_500_000.0, isCash: true)
        #expect(result1 == "$25.5 Million")
        
        let result2 = DataFormatter.formatCurrency(25_000_000.0, isCash: true)
        #expect(result2 == "$25 Million")
        
        let result3 = DataFormatter.formatCurrency(25_250_000.0, isCash: true)
        #expect(result3 == "$25.2 Million")
    }
    
    @Test func testFormatCurrencyBillions() {
        // Test amounts in billions
        let result1 = DataFormatter.formatCurrency(1_000_000_000.0)
        #expect(result1 == "$1.0 Billion")
        
        let result2 = DataFormatter.formatCurrency(1_200_000_000.0)
        #expect(result2 == "$1.2 Billion")
        
        let result3 = DataFormatter.formatCurrency(15_500_000_000.0)
        #expect(result3 == "$15.5 Billion")
    }
    
    @Test func testFormatCurrencyBillionsWithCash() {
        // Test cash mode for billions (should always show 1 decimal place)
        let result1 = DataFormatter.formatCurrency(1_000_000_000.0, isCash: true)
        #expect(result1 == "$1.0 Billion")
        
        let result2 = DataFormatter.formatCurrency(2_500_000_000.0, isCash: true)
        #expect(result2 == "$2.5 Billion")
    }
    
    @Test func testFormatCurrencyEdgeCases() {
        // Test edge cases at boundaries
        let result1 = DataFormatter.formatCurrency(999_999.99)
        #expect(result1 == "$1,000,000")
        
        let result2 = DataFormatter.formatCurrency(1_000_000.01)
        #expect(result2 == "$1 Million")
        
        let result3 = DataFormatter.formatCurrency(999_999_999.99)
        #expect(result3 == "$1,000 Million")
    }
    
    @Test func testFormatCurrencyZero() {
        // Test zero amount
        let result = DataFormatter.formatCurrency(0.0)
        #expect(result == "$0")
    }
    
    // MARK: - Format Widget Next Drawing Date Tests
    
    @Test func testFormatWidgetNextDrawingDateToday() throws {
        // Test when drawing date is today - should show "Today, [time]"
        let calendar = Calendar.current
        
        // Create a date for today at 8:00 PM
        var todayComponents = calendar.dateComponents([.year, .month, .day], from: Date())
        todayComponents.hour = 20  // 8PM
        todayComponents.minute = 0
        
        let todayDate = try #require(calendar.date(from: todayComponents))
        let result = DataFormatter.formatWidgetNextDrawingDate(todayDate)
        
        #expect(result == "Today, 8PM")
    }
    
    @Test func testFormatWidgetNextDrawingDateTodayAM() throws {
        // Test when drawing date is today in AM - should show "Today, [time]AM"
        let calendar = Calendar.current
        
        // Create a date for today at 10:00 AM
        var todayComponents = calendar.dateComponents([.year, .month, .day], from: Date())
        todayComponents.hour = 10  // 10AM
        todayComponents.minute = 0
        
        let todayDate = try #require(calendar.date(from: todayComponents))
        let result = DataFormatter.formatWidgetNextDrawingDate(todayDate)
        
        #expect(result == "Today, 10AM")
    }
    
    @Test func testFormatWidgetNextDrawingDateTodayMidnight() throws {
        // Test when drawing date is today at midnight
        let calendar = Calendar.current
        
        // Create a date for today at 12:00 AM
        var todayComponents = calendar.dateComponents([.year, .month, .day], from: Date())
        todayComponents.hour = 0  // 12AM
        todayComponents.minute = 0
        
        let todayDate = try #require(calendar.date(from: todayComponents))
        let result = DataFormatter.formatWidgetNextDrawingDate(todayDate)
        
        #expect(result == "Today, 12AM")
    }
    
    @Test func testFormatWidgetNextDrawingDateTodayNoon() throws {
        // Test when drawing date is today at noon
        let calendar = Calendar.current
        
        // Create a date for today at 12:00 PM
        var todayComponents = calendar.dateComponents([.year, .month, .day], from: Date())
        todayComponents.hour = 12  // 12PM
        todayComponents.minute = 0
        
        let todayDate = try #require(calendar.date(from: todayComponents))
        let result = DataFormatter.formatWidgetNextDrawingDate(todayDate)
        
        #expect(result == "Today, 12PM")
    }
    
    @Test func testFormatWidgetNextDrawingDateTomorrow() throws {
        // Test when drawing date is tomorrow - should show full date format
        let calendar = Calendar.current
        
        // Create a date for tomorrow
        let tomorrow = calendar.date(byAdding: .day, value: 1, to: Date())!
        let result = DataFormatter.formatWidgetNextDrawingDate(tomorrow)
        
        // Should be in format "EEEE, MMM d" (e.g., "Friday, Aug 2")
        let expectedFormatter = DateFormatter()
        expectedFormatter.dateFormat = "EEEE, MMM d"
        let expected = expectedFormatter.string(from: tomorrow)
        
        #expect(result == expected)
    }
    
    @Test func testFormatWidgetNextDrawingDateFuture() throws {
        // Test when drawing date is several days in the future
        let calendar = Calendar.current
        
        // Create a date for 5 days from now
        let futureDate = calendar.date(byAdding: .day, value: 5, to: Date())!
        let result = DataFormatter.formatWidgetNextDrawingDate(futureDate)
        
        // Should be in format "EEEE, MMM d" (e.g., "Tuesday, Aug 6")
        let expectedFormatter = DateFormatter()
        expectedFormatter.dateFormat = "EEEE, MMM d"
        let expected = expectedFormatter.string(from: futureDate)
        
        #expect(result == expected)
    }
    
    @Test func testFormatWidgetNextDrawingDateSpecificDate() throws {
        // Test with a specific known date to ensure consistent formatting
        let calendar = Calendar.current
        
        // Create a specific date: August 15, 2025 at 8:00 PM
        var components = DateComponents()
        components.year = 2025
        components.month = 8
        components.day = 15
        components.hour = 20
        components.minute = 0
        
        let specificDate = try #require(calendar.date(from: components))
        let result = DataFormatter.formatWidgetNextDrawingDate(specificDate)
        
        // Since this is not today (assuming tests run before 2025), should show full format
        // Expected: "Friday, Aug 15" (August 15, 2025 is a Friday)
        #expect(result == "Friday, Aug 15")
    }
    
    @Test func testFormatWidgetNextDrawingDateWithMinutes() throws {
        // Test that minutes are ignored in the time format (should only show hour)
        let calendar = Calendar.current
        
        // Create a date for today at 8:30 PM (with minutes)
        var todayComponents = calendar.dateComponents([.year, .month, .day], from: Date())
        todayComponents.hour = 20  // 8PM
        todayComponents.minute = 30  // 30 minutes
        
        let todayDate = try #require(calendar.date(from: todayComponents))
        let result = DataFormatter.formatWidgetNextDrawingDate(todayDate)
        
        // Should still show "Today, 8PM" (ignoring the 30 minutes)
        #expect(result == "Today, 8PM")
    }
} 
