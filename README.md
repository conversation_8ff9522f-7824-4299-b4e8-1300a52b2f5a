# 🎰 LotteryLightbox

A cross-platform SwiftUI application that displays lottery information (Powerball and Mega Millions) with an authentic digital street lightbox aesthetic. The app supports iOS, macOS, and visionOS with integrated WidgetKit support.

## ✨ Features

### Authentic Lightbox Design
- **LED Typography**: Monospaced digital fonts with authentic glow effects
- **Glass Reflection**: Subtle reflective overlay simulating real lightbox glass
- **Vibrant Colors**: High-contrast LED colors (green, red, blue, yellow) for maximum visibility

### Multi-Platform Support
- **iOS**: 18.5+ (iPhone, iPad)
- **macOS**: 15.0+
- **visionOS**: 2.5+

### Widget Support
- **Small Widget (2x2)**: Quick jackpot checks with single lottery display
- **Medium Widget (4x2)**: Powerball and Mega Millions side-by-side
- **Large Widget (4x4)**: Comprehensive dual lottery display with winner information

### Smart Features
- **Automatic Data Refresh**: Updates every 30 minutes
- **Smart Text Formatting**: Automatic jackpot abbreviation (e.g., "$1.2B")
- **Relative Date Formatting**: "Tomorrow", "In 2 days", etc.
- **Error Handling**: Graceful degradation with LED-style error messages

## 🏗️ Architecture

### Core Technologies
- **SwiftUI**: Primary UI framework
- **WidgetKit**: Widget extensions for all platforms
- **SwiftSoup**: HTML parsing for web scraping of Powerball
- **App Groups**: Data sharing between app and widgets

### Data Flow
1. **Web Scraping**: Services fetch data from official lottery websites
2. **Data Processing**: Parse and format lottery information
3. **App Group Storage**: Share data between main app and widget
4. **UI Rendering**: Display with authentic lightbox aesthetic

### Services
- **PowerballService**: Scrapes Powerball data from powerball.com
- **MegaMillionsService**: Fetches Mega Millions data via API
- **LotteryDataManager**: Manages data sharing and caching

## 🎨 Design System

### Color Scheme
- **Jackpot Value**: Bright red LED digits (`#FF334D`)
- **Secondary Info**: Blue (`#66CCFF`) for cash values
- **Drawing Dates**: Yellow (`#FFCC33`) for drawing information
- **Winners**: Green for winner information

### Typography
- **Monospaced Fonts**: Authentic LED display appearance
- **Glow Effects**: Multiple shadow layers for realistic lighting
- **Responsive Sizing**: Scales appropriately for different widget sizes

### Layout Hierarchy
1. **Primary**: Jackpot amounts (largest, brightest)
2. **Secondary**: Lottery branding and logos
3. **Tertiary**: Drawing dates and cash values
4. **Quaternary**: Labels and status indicators

## 🚀 Getting Started

### Prerequisites
- Xcode 16.0+
- iOS 18.5+ / macOS 15.0+ / visionOS 2.5+
- Apple Developer Account (for App Groups)

### Installation
1. Clone the repository
2. Open `LotteryLightBoxAugment.xcodeproj` in Xcode
3. Configure App Groups in your Apple Developer account
4. Update the App Group identifier in both targets
5. Build and run

### Configuration
- **App Groups**: Ensure both main app and widget extension have the same App Group
- **Network Permissions**: App requires internet access for data fetching
- **Background Refresh**: Enable for automatic widget updates

## 📱 Widget Configuration

### Widget Sizes
- **Small**: Single lottery display with jackpot and next drawing
- **Medium**: Dual lottery display side-by-side
- **Large**: Comprehensive display with winner information

### Configuration Options
- **Lottery Type**: Choose Powerball, Mega Millions, or Both
- **Auto Refresh**: Updates every 30 minutes
- **Error States**: Graceful handling of network issues

## 🔧 Development

### Project Structure
```
LotteryLightBoxAugment/
├── Model/
│   └── DrawingInfo.swift          # Data models
├── Service/
│   ├── LotteryServiceProtocol.swift
│   ├── PowerballService.swift     # Powerball data fetching
│   ├── MegaMillionsService.swift  # Mega Millions data fetching
│   └── LotteryDataManager.swift   # Data sharing management
├── Views/
│   ├── LEDTextView.swift          # LED-style text components
│   └── LotteryCardView.swift      # Lottery card display
├── ContentView.swift              # Main app view
└── LotteryLightBoxAugmentApp.swift

LotteryLightBoxWidget/
├── LEDWidgetViews.swift           # Widget-specific LED components
├── WidgetDataManager.swift        # Widget data loading
├── LotteryLightBoxWidget.swift    # Widget implementation
└── AppIntent.swift                # Widget configuration
```

### Key Components

#### LEDTextView
Reusable LED-style text component with glow effects and authentic typography.

#### LotteryDataManager
Manages data sharing between app and widget using App Groups with UserDefaults.

#### Widget Implementation
Supports multiple widget sizes with responsive design and error handling.

## 🧪 Testing

### Unit Tests
- Service layer testing
- Data model validation
- Web scraping reliability
- Widget data management

### Test Data
- Local HTML files for scraping tests
- Mock lottery data for UI testing
- Widget preview data

## 🔒 Security & Privacy

### Data Handling
- **No Personal Data**: App doesn't collect or store personal information
- **Local Storage**: All data stored locally via App Groups
- **Network Security**: Secure HTTPS connections to official lottery sites

### Web Scraping
- **Respectful Scraping**: Implements rate limiting and proper user agents
- **Error Handling**: Graceful degradation when data unavailable
- **Caching**: Reduces server load with intelligent caching

## 🚀 Performance

### Optimization Strategies
- **Efficient Scraping**: Minimal network requests
- **Background Tasks**: Non-blocking data updates
- **Memory Management**: Efficient data sharing
- **Widget Optimization**: Minimal refresh intervals

### Target Metrics
- App launch time: < 2 seconds
- Widget refresh: < 5 seconds
- Memory usage: < 50MB
- Network requests: < 1MB per update

## 🎯 Future Enhancements

### Planned Features
- Additional lottery games (Lotto America, etc.)
- Historical data tracking
- Push notifications for jackpot updates
- Customizable widget themes
- Apple Watch support

### Technical Improvements
- Enhanced error handling
- Offline data caching
- Advanced widget configurations
- Accessibility improvements

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For support and questions, please open an issue on GitHub.

---

**Note**: This app is for informational purposes only. Please gamble responsibly and check official lottery websites for the most current information. 